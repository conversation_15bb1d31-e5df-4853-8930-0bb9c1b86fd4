import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Navbar from "@/components/Navbar";
import { 
  Wallet, 
  TrendingUp, 
  TrendingDown, 
  Plus, 
  Receipt, 
  Calendar,
  DollarSign,
  FileText,
  Edit,
  Trash2,
  Download
} from "lucide-react";
import { formatLibyanDinar } from "@/utils/calculations";
import {
  getCashTransactions,
  getCashSummary,
  saveCashTransaction,
  getProjects,
  getEmployees,
  CashTransaction
} from "@/utils/dataManager";
import { useToast } from "@/hooks/use-toast";

const Treasury = () => {
  const { toast } = useToast();
  const [transactions, setTransactions] = useState<CashTransaction[]>([]);
  const [summary, setSummary] = useState({
    totalIncome: 0,
    totalExpenses: 0,
    currentBalance: 0,
    monthlyIncome: 0,
    monthlyExpenses: 0,
    projectPayments: 0,
    salaryPayments: 0,
    generalExpenses: 0
  });
  const [editingTransaction, setEditingTransaction] = useState<CashTransaction | null>(null);
  const [loading, setLoading] = useState(true);
  
  // نموذج المعاملة الجديدة
  const [newTransaction, setNewTransaction] = useState({
    type: 'دخل' as 'دخل' | 'مصروف',
    category: 'أخرى',
    amount: '',
    description: '',
    paymentMethod: 'نقدي' as 'نقدي' | 'تحويل بنكي' | 'شيك',
    notes: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [transactionsData, summaryData] = await Promise.all([
        getCashTransactions(),
        getCashSummary()
      ]);
      setTransactions(transactionsData);
      setSummary(summaryData);
    } catch (error) {
      console.error('خطأ في تحميل بيانات الخزينة:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات الخزينة",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddTransaction = async () => {
    if (!newTransaction.amount || !newTransaction.description) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    try {
      const transaction = {
        type: newTransaction.type,
        category: newTransaction.category,
        amount: parseFloat(newTransaction.amount),
        description: newTransaction.description,
        date: new Date().toISOString().split('T')[0]
      };

      const id = await saveCashTransaction(transaction);
      if (id) {
        await loadData();
        resetForm();

        toast({
          title: "تم بنجاح",
          description: "تم إضافة المعاملة إلى الخزينة",
        });
      } else {
        toast({
          title: "خطأ",
          description: "فشل في حفظ المعاملة",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('خطأ في إضافة المعاملة:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء حفظ المعاملة",
        variant: "destructive",
      });
    }
  };

  const handleEditTransaction = (transaction: CashTransaction) => {
    setEditingTransaction(transaction);
    setNewTransaction({
      type: transaction.type,
      category: transaction.category,
      amount: transaction.amount.toString(),
      description: transaction.description,
      paymentMethod: transaction.paymentMethod,
      notes: transaction.notes || ''
    });
  };

  const handleUpdateTransaction = () => {
    // سيتم تنفيذها لاحقاً - تحتاج إلى إضافة وظيفة تحديث في قاعدة البيانات
    toast({
      title: "قريباً",
      description: "ميزة التعديل ستكون متاحة قريباً",
    });
  };

  const handleDeleteTransaction = (id: string) => {
    // سيتم تنفيذها لاحقاً - تحتاج إلى إضافة وظيفة حذف في قاعدة البيانات
    toast({
      title: "قريباً",
      description: "ميزة الحذف ستكون متاحة قريباً",
    });
  };

  const resetForm = () => {
    setNewTransaction({
      type: 'دخل',
      category: 'أخرى',
      amount: '',
      description: '',
      paymentMethod: 'نقدي',
      notes: ''
    });
  };

  const handleQuickPayProjectBalance = async () => {
    try {
      const projects = await getProjects();
      const pendingProjects = projects.filter(p => p.remainingAmount > 0);

      if (pendingProjects.length === 0) {
        toast({
          title: "تنبيه",
          description: "لا توجد مشاريع بها مبالغ متبقية",
        });
        return;
      }

      // عرض أول مشروع به مبلغ متبقي
      const project = pendingProjects[0];
      setNewTransaction({
        type: 'دخل',
        category: 'دفعة مشروع',
        amount: project.remainingAmount.toString(),
        description: `دفعة متبقية من مشروع ${project.customerName}`,
        paymentMethod: 'نقدي',
        notes: `مشروع: ${project.furnitureType}`
      });
    } catch (error) {
      console.error('خطأ في جلب المشاريع:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء جلب المشاريع",
        variant: "destructive"
      });
    }
  };

  const handleQuickPaySalary = async () => {
    try {
      const employees = await getEmployees();

      if (employees.length === 0) {
        toast({
          title: "تنبيه",
          description: "لا يوجد موظفين مسجلين",
        });
        return;
      }

      // عرض أول موظف
      const employee = employees[0];
      setNewTransaction({
        type: 'مصروف',
        category: 'راتب',
        amount: employee.totalSalary.toString(),
        description: `راتب ${employee.name} - ${employee.position}`,
        paymentMethod: 'نقدي',
        notes: `راتب شهر ${new Date().toLocaleDateString('ar-LY', { month: 'long', year: 'numeric' })}`
      });
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء جلب الموظفين",
        variant: "destructive"
      });
    }
  };

  const exportTransactions = () => {
    const exportData = `
تقرير الخزينة - ${new Date().toLocaleDateString('ar-LY')}
===========================================

الملخص:
- إجمالي الدخل: ${formatLibyanDinar(summary.totalIncome)}
- إجمالي المصروفات: ${formatLibyanDinar(summary.totalExpenses)}
- الرصيد الحالي: ${formatLibyanDinar(summary.currentBalance)}

المعاملات:
${transactions.map(t => `
التاريخ: ${t.date}
النوع: ${t.type}
الفئة: ${t.category}
المبلغ: ${formatLibyanDinar(t.amount)}
الوصف: ${t.description}
طريقة الدفع: ${t.paymentMethod}
${t.notes ? `ملاحظات: ${t.notes}` : ''}
---
`).join('')}
`;

    const blob = new Blob([exportData], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_الخزينة_${new Date().toLocaleDateString('ar-LY').replace(/\//g, '-')}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-3">
            <Wallet className="h-10 w-10 text-green-600" />
            نظام إدارة الخزينة
          </h1>
          <p className="text-xl text-gray-600">
            متابعة وإدارة التدفقات النقدية للمصنع
          </p>
        </header>

        {/* ملخص الخزينة */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">الرصيد الحالي</p>
                  <p className={`text-2xl font-bold ${summary.currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatLibyanDinar(summary.currentBalance)}
                  </p>
                </div>
                <DollarSign className={`h-8 w-8 ${summary.currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}`} />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">إجمالي الدخل</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatLibyanDinar(summary.totalIncome)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">إجمالي المصروفات</p>
                  <p className="text-2xl font-bold text-red-600">
                    {formatLibyanDinar(summary.totalExpenses)}
                  </p>
                </div>
                <TrendingDown className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">صافي الشهر الحالي</p>
                  <p className={`text-2xl font-bold ${(summary.monthlyIncome - summary.monthlyExpenses) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatLibyanDinar(summary.monthlyIncome - summary.monthlyExpenses)}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="transactions" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="transactions">المعاملات</TabsTrigger>
            <TabsTrigger value="add">إضافة معاملة</TabsTrigger>
          </TabsList>

          <TabsContent value="transactions" className="space-y-6">
            <Card className="shadow-xl">
              <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Receipt className="h-6 w-6" />
                    سجل المعاملات
                  </span>
                  <Button
                    onClick={exportTransactions}
                    variant="secondary"
                    size="sm"
                    className="bg-white/20 hover:bg-white/30 text-white"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    تصدير
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">جاري تحميل المعاملات...</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>التاريخ</TableHead>
                          <TableHead>النوع</TableHead>
                          <TableHead>الفئة</TableHead>
                          <TableHead>المبلغ</TableHead>
                          <TableHead>الوصف</TableHead>
                          <TableHead>العمليات</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                          {transactions.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                              لا توجد معاملات مسجلة
                            </TableCell>
                          </TableRow>
                        ) : (
                          transactions.map((transaction) => (
                            <TableRow key={transaction.id}>
                              <TableCell>{transaction.date}</TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded text-sm ${
                                  transaction.type === 'دخل'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {transaction.type}
                                </span>
                              </TableCell>
                              <TableCell>{transaction.category}</TableCell>
                              <TableCell className={
                                transaction.type === 'دخل' ? 'text-green-600 font-semibold' : 'text-red-600 font-semibold'
                              }>
                                {formatLibyanDinar(transaction.amount)}
                              </TableCell>
                              <TableCell>{transaction.description}</TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleEditTransaction(transaction)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleDeleteTransaction(transaction.id)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="add" className="space-y-6">
            <Card className="shadow-xl">
              <CardHeader className="bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-t-lg">
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-6 w-6" />
                  {editingTransaction ? 'تعديل معاملة' : 'إضافة معاملة جديدة'}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-6">
                {/* أزرار سريعة */}
                <div className="flex gap-4 mb-6">
                  <Button
                    onClick={handleQuickPayProjectBalance}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Receipt className="h-4 w-4" />
                    دفعة مشروع متبقية
                  </Button>
                  <Button
                    onClick={handleQuickPaySalary}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    دفع راتب
                  </Button>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>نوع المعاملة *</Label>
                    <Select value={newTransaction.type} onValueChange={(value: 'دخل' | 'مصروف') => 
                      setNewTransaction(prev => ({ ...prev, type: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر نوع المعاملة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="دخل">دخل</SelectItem>
                        <SelectItem value="مصروف">مصروف</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>فئة المعاملة *</Label>
                    <Select value={newTransaction.category} onValueChange={(value) => 
                      setNewTransaction(prev => ({ ...prev, category: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الفئة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="مشروع">مشروع</SelectItem>
                        <SelectItem value="مرتب">مرتب</SelectItem>
                        <SelectItem value="مصروفات عامة">مصروفات عامة</SelectItem>
                        <SelectItem value="شراء مواد">شراء مواد</SelectItem>
                        <SelectItem value="صيانة">صيانة</SelectItem>
                        <SelectItem value="كهرباء">كهرباء</SelectItem>
                        <SelectItem value="ايجار">إيجار</SelectItem>
                        <SelectItem value="أخرى">أخرى</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>المبلغ (د.ل) *</Label>
                    <Input
                      type="number"
                      placeholder="أدخل المبلغ"
                      value={newTransaction.amount}
                      onChange={(e) => setNewTransaction(prev => ({ ...prev, amount: e.target.value }))}
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>طريقة الدفع *</Label>
                    <Select value={newTransaction.paymentMethod} onValueChange={(value: 'نقدي' | 'تحويل بنكي' | 'شيك') => 
                      setNewTransaction(prev => ({ ...prev, paymentMethod: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر طريقة الدفع" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="نقدي">نقدي</SelectItem>
                        <SelectItem value="تحويل بنكي">تحويل بنكي</SelectItem>
                        <SelectItem value="شيك">شيك</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label>وصف المعاملة *</Label>
                    <Input
                      type="text"
                      placeholder="أدخل وصف المعاملة"
                      value={newTransaction.description}
                      onChange={(e) => setNewTransaction(prev => ({ ...prev, description: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label>ملاحظات إضافية</Label>
                    <Textarea
                      placeholder="ملاحظات اختيارية..."
                      value={newTransaction.notes}
                      onChange={(e) => setNewTransaction(prev => ({ ...prev, notes: e.target.value }))}
                      rows={3}
                    />
                  </div>
                </div>

                <div className="flex gap-4 justify-center pt-4">
                  <Button
                    onClick={editingTransaction ? handleUpdateTransaction : handleAddTransaction}
                    className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white px-8 py-3"
                  >
                    {editingTransaction ? 'تحديث المعاملة' : 'إضافة المعاملة'}
                  </Button>
                  {editingTransaction && (
                    <Button
                      onClick={() => {
                        setEditingTransaction(null);
                        resetForm();
                      }}
                      variant="outline"
                      className="px-8 py-3"
                    >
                      إلغاء التعديل
                    </Button>
                  )}
                  <Button
                    onClick={resetForm}
                    variant="outline"
                    className="px-8 py-3"
                  >
                    مسح النموذج
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Treasury;
