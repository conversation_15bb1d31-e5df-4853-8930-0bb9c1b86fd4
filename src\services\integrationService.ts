// خدمة التكامل الشامل بين أقسام التطبيق
import { 
  IntegrationEvent, 
  MaterialIntegration, 
  LaborCostIntegration, 
  TreasuryIntegration,
  ProjectCostBreakdown,
  NotificationAlert,
  IntegrationConfig,
  SystemSync
} from '@/types/integration';
import { ProjectMaterialsSummary } from '@/types/materials';
import { runQuery, getQuery, allQuery } from '@/utils/database';

// التحقق من وجود Electron API
const isElectron = typeof window !== 'undefined' && window.electronAPI;

class IntegrationService {
  private static config: IntegrationConfig = {
    autoUpdateMaterials: true,
    autoCalculateLaborCosts: true,
    autoCreateInvoices: true,
    autoUpdateTreasury: true,
    enableNotifications: true,
    syncInterval: 5 // كل 5 دقائق
  };

  private static eventQueue: IntegrationEvent[] = [];
  private static notifications: NotificationAlert[] = [];

  // إعداد التكامل وإنشاء الجداول المطلوبة
  static async initialize(): Promise<void> {
    try {
      await this.createIntegrationTables();
      await this.loadConfiguration();
      this.startSyncProcess();
    } catch (error) {
      console.error('خطأ في تهيئة نظام التكامل:', error);
    }
  }

  // إنشاء جداول التكامل في قاعدة البيانات
  private static async createIntegrationTables(): Promise<void> {
    const tables = [
      // جدول أحداث التكامل
      `CREATE TABLE IF NOT EXISTS integration_events (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        source TEXT NOT NULL,
        target TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        processed INTEGER DEFAULT 0
      )`,
      
      // جدول تكامل المواد
      `CREATE TABLE IF NOT EXISTS material_integrations (
        id TEXT PRIMARY KEY,
        material_id TEXT NOT NULL,
        project_id TEXT NOT NULL,
        required_quantity REAL NOT NULL,
        available_quantity REAL NOT NULL,
        reserved_quantity REAL DEFAULT 0,
        cost_per_unit REAL NOT NULL,
        total_cost REAL NOT NULL,
        source TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )`,
      
      // جدول تكامل العمالة
      `CREATE TABLE IF NOT EXISTS labor_integrations (
        id TEXT PRIMARY KEY,
        worker_id TEXT NOT NULL,
        project_id TEXT NOT NULL,
        estimated_hours REAL NOT NULL,
        hourly_rate REAL NOT NULL,
        total_labor_cost REAL NOT NULL,
        skill_level TEXT NOT NULL,
        availability INTEGER DEFAULT 1,
        created_at TEXT NOT NULL
      )`,
      
      // جدول التنبيهات
      `CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        source TEXT NOT NULL,
        priority TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        read INTEGER DEFAULT 0,
        action_required INTEGER DEFAULT 0,
        related_id TEXT
      )`,
      
      // جدول إعدادات التكامل
      `CREATE TABLE IF NOT EXISTS integration_config (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )`
    ];

    for (const table of tables) {
      if (isElectron) {
        await window.electronAPI.runQuery(table, []);
      }
    }
  }

  // تحميل إعدادات التكامل
  private static async loadConfiguration(): Promise<void> {
    try {
      if (isElectron) {
        const configs = await window.electronAPI.allQuery('SELECT * FROM integration_config', []);
        configs.forEach((config: any) => {
          if (config.key in this.config) {
            this.config[config.key as keyof IntegrationConfig] = JSON.parse(config.value);
          }
        });
      }
    } catch (error) {
      console.error('خطأ في تحميل إعدادات التكامل:', error);
    }
  }

  // حفظ إعدادات التكامل
  static async saveConfiguration(newConfig: Partial<IntegrationConfig>): Promise<void> {
    try {
      this.config = { ...this.config, ...newConfig };
      
      if (isElectron) {
        for (const [key, value] of Object.entries(newConfig)) {
          await window.electronAPI.runQuery(
            'INSERT OR REPLACE INTO integration_config (key, value, updated_at) VALUES (?, ?, ?)',
            [key, JSON.stringify(value), new Date().toISOString()]
          );
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ إعدادات التكامل:', error);
    }
  }

  // إضافة حدث تكامل جديد
  static async addIntegrationEvent(event: Omit<IntegrationEvent, 'id' | 'timestamp' | 'processed'>): Promise<string> {
    const newEvent: IntegrationEvent = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      processed: false,
      ...event
    };

    try {
      if (isElectron) {
        await window.electronAPI.runQuery(
          'INSERT INTO integration_events (id, type, source, target, data, timestamp, processed) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [newEvent.id, newEvent.type, newEvent.source, JSON.stringify(newEvent.target), JSON.stringify(newEvent.data), newEvent.timestamp, 0]
        );
      }
      
      this.eventQueue.push(newEvent);
      await this.processEvent(newEvent);
      
      return newEvent.id;
    } catch (error) {
      console.error('خطأ في إضافة حدث التكامل:', error);
      throw error;
    }
  }

  // معالجة حدث تكامل
  private static async processEvent(event: IntegrationEvent): Promise<void> {
    try {
      switch (event.type) {
        case 'cost_calculated':
          await this.handleCostCalculated(event);
          break;
        case 'project_created':
          await this.handleProjectCreated(event);
          break;
        case 'material_update':
          await this.handleMaterialUpdate(event);
          break;
        case 'payment_received':
          await this.handlePaymentReceived(event);
          break;
        case 'salary_calculated':
          await this.handleSalaryCalculated(event);
          break;
      }

      // تحديث حالة المعالجة
      if (isElectron) {
        await window.electronAPI.runQuery(
          'UPDATE integration_events SET processed = 1 WHERE id = ?',
          [event.id]
        );
      }
    } catch (error) {
      console.error('خطأ في معالجة حدث التكامل:', error);
      await this.addNotification({
        type: 'error',
        title: 'خطأ في التكامل',
        message: `فشل في معالجة حدث ${event.type}`,
        source: 'integration_service',
        priority: 'high',
        actionRequired: true,
        relatedId: event.id
      });
    }
  }

  // معالجة حدث حساب التكلفة
  private static async handleCostCalculated(event: IntegrationEvent): Promise<void> {
    const { projectData, materialsSummary, laborCosts } = event.data;

    if (this.config.autoUpdateMaterials && materialsSummary) {
      await this.updateMaterialReservations(projectData.id, materialsSummary);
    }

    if (this.config.autoUpdateTreasury) {
      await this.updateTreasuryProjections(projectData, materialsSummary, laborCosts);
    }

    if (this.config.autoCreateInvoices) {
      await this.createProjectInvoice(projectData);
    }
  }

  // تحديث حجوزات المواد
  private static async updateMaterialReservations(projectId: string, materialsSummary: ProjectMaterialsSummary): Promise<void> {
    try {
      for (const material of materialsSummary.materials) {
        const integration: MaterialIntegration = {
          materialId: material.materialId,
          projectId: projectId,
          requiredQuantity: material.requiredQuantity,
          availableQuantity: 0, // سيتم تحديثها من قاعدة البيانات
          reservedQuantity: material.requiredQuantity,
          costPerUnit: material.purchasePrice,
          totalCost: material.totalPurchase,
          source: material.source,
          status: 'reserved'
        };

        await this.saveMaterialIntegration(integration);
        
        // تحديث الكمية المتاحة في المخزن
        if (material.source === 'stock') {
          await this.updateMaterialStock(material.materialId, -material.requiredQuantity);
        }
      }
    } catch (error) {
      console.error('خطأ في تحديث حجوزات المواد:', error);
    }
  }

  // حفظ تكامل المواد
  private static async saveMaterialIntegration(integration: MaterialIntegration): Promise<void> {
    if (isElectron) {
      await window.electronAPI.runQuery(
        `INSERT OR REPLACE INTO material_integrations 
         (id, material_id, project_id, required_quantity, available_quantity, reserved_quantity, 
          cost_per_unit, total_cost, source, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          Date.now().toString(),
          integration.materialId,
          integration.projectId,
          integration.requiredQuantity,
          integration.availableQuantity,
          integration.reservedQuantity,
          integration.costPerUnit,
          integration.totalCost,
          integration.source,
          integration.status,
          new Date().toISOString(),
          new Date().toISOString()
        ]
      );
    }
  }

  // تحديث مخزون المواد
  private static async updateMaterialStock(materialId: string, quantityChange: number): Promise<void> {
    if (isElectron) {
      await window.electronAPI.runQuery(
        'UPDATE detailed_materials SET availableQuantity = availableQuantity + ? WHERE id = ?',
        [quantityChange, materialId]
      );
    }
  }

  // إضافة تنبيه
  static async addNotification(notification: Omit<NotificationAlert, 'id' | 'timestamp' | 'read'>): Promise<string> {
    const newNotification: NotificationAlert = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    };

    try {
      if (isElectron) {
        await window.electronAPI.runQuery(
          'INSERT INTO notifications (id, type, title, message, source, priority, timestamp, read, action_required, related_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [
            newNotification.id,
            newNotification.type,
            newNotification.title,
            newNotification.message,
            newNotification.source,
            newNotification.priority,
            newNotification.timestamp,
            0,
            newNotification.actionRequired ? 1 : 0,
            newNotification.relatedId || null
          ]
        );
      }

      this.notifications.push(newNotification);
      return newNotification.id;
    } catch (error) {
      console.error('خطأ في إضافة التنبيه:', error);
      throw error;
    }
  }

  // بدء عملية المزامنة
  private static startSyncProcess(): void {
    if (this.config.syncInterval > 0) {
      setInterval(async () => {
        await this.syncPendingEvents();
      }, this.config.syncInterval * 60 * 1000);
    }
  }

  // مزامنة الأحداث المعلقة
  private static async syncPendingEvents(): Promise<void> {
    try {
      if (isElectron) {
        const pendingEvents = await window.electronAPI.allQuery(
          'SELECT * FROM integration_events WHERE processed = 0 ORDER BY timestamp ASC',
          []
        );

        for (const eventData of pendingEvents) {
          const event: IntegrationEvent = {
            id: eventData.id,
            type: eventData.type,
            source: eventData.source,
            target: JSON.parse(eventData.target),
            data: JSON.parse(eventData.data),
            timestamp: eventData.timestamp,
            processed: false
          };

          await this.processEvent(event);
        }
      }
    } catch (error) {
      console.error('خطأ في مزامنة الأحداث المعلقة:', error);
    }
  }

  // الحصول على التنبيهات
  static async getNotifications(unreadOnly: boolean = false): Promise<NotificationAlert[]> {
    try {
      if (isElectron) {
        const query = unreadOnly 
          ? 'SELECT * FROM notifications WHERE read = 0 ORDER BY timestamp DESC'
          : 'SELECT * FROM notifications ORDER BY timestamp DESC';
        
        const notifications = await window.electronAPI.allQuery(query, []);
        return notifications.map((n: any) => ({
          ...n,
          read: Boolean(n.read),
          actionRequired: Boolean(n.action_required)
        }));
      }
      return [];
    } catch (error) {
      console.error('خطأ في جلب التنبيهات:', error);
      return [];
    }
  }

  // تحديث حالة المشروع في الخزينة (سيتم تنفيذها لاحقاً)
  private static async updateTreasuryProjections(projectData: any, materialsSummary: any, laborCosts: any): Promise<void> {
    // سيتم تنفيذها في الجزء التالي
  }

  // إنشاء فاتورة المشروع (سيتم تنفيذها لاحقاً)
  private static async createProjectInvoice(projectData: any): Promise<void> {
    // سيتم تنفيذها في الجزء التالي
  }

  // معالجة الأحداث الأخرى (سيتم تنفيذها لاحقاً)
  private static async handleProjectCreated(event: IntegrationEvent): Promise<void> {}
  private static async handleMaterialUpdate(event: IntegrationEvent): Promise<void> {}
  private static async handlePaymentReceived(event: IntegrationEvent): Promise<void> {}
  private static async handleSalaryCalculated(event: IntegrationEvent): Promise<void> {}
}

export default IntegrationService;
