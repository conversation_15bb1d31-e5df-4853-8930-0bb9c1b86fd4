import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  FileText, 
  Factory, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  Eye,
  Edit
} from 'lucide-react';
import { getProjects, updateProject, Project } from '@/utils/dataManager';
import { formatLibyanDinar } from '@/utils/calculations';
import { useToast } from '@/hooks/use-toast';

interface ProjectLifecycleManagerProps {
  onProjectUpdate?: () => void;
}

const ProjectLifecycleManager: React.FC<ProjectLifecycleManagerProps> = ({ onProjectUpdate }) => {
  const { toast } = useToast();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const projectsData = await getProjects();
      setProjects(Array.isArray(projectsData) ? projectsData : []);
    } catch (error) {
      console.error('خطأ في تحميل المشاريع:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل المشاريع",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateProjectStatus = async (projectId: string, newStatus: Project['status']) => {
    try {
      const success = await updateProject(projectId, { status: newStatus });
      if (success) {
        setProjects(prev => prev.map(project =>
          project.id === projectId ? { ...project, status: newStatus } : project
        ));
        toast({
          title: "تم تحديث حالة المشروع",
          description: "تم حفظ التغييرات بنجاح"
        });
        onProjectUpdate?.();
      } else {
        throw new Error('فشل في تحديث المشروع');
      }
    } catch (error) {
      console.error('خطأ في تحديث المشروع:', error);
      toast({
        title: "خطأ في التحديث",
        description: "حدث خطأ أثناء تحديث حالة المشروع",
        variant: "destructive",
      });
    }
  };

  const updateInvoiceStatus = async (projectId: string, newInvoiceStatus: Project['invoiceStatus']) => {
    try {
      const success = await updateProject(projectId, { invoiceStatus: newInvoiceStatus });
      if (success) {
        setProjects(prev => prev.map(project =>
          project.id === projectId ? { ...project, invoiceStatus: newInvoiceStatus } : project
        ));
        toast({
          title: "تم تحديث حالة الفاتورة",
          description: "تم حفظ التغييرات بنجاح"
        });
        onProjectUpdate?.();
      } else {
        throw new Error('فشل في تحديث الفاتورة');
      }
    } catch (error) {
      console.error('خطأ في تحديث الفاتورة:', error);
      toast({
        title: "خطأ في التحديث",
        description: "حدث خطأ أثناء تحديث حالة الفاتورة",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: Project['status']) => {
    switch (status) {
      case 'مكتمل':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'قيد التنفيذ':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'متأخر':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'ملغي':
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'مكتمل':
        return 'bg-green-100 text-green-800';
      case 'قيد التنفيذ':
        return 'bg-blue-100 text-blue-800';
      case 'متأخر':
        return 'bg-red-100 text-red-800';
      case 'ملغي':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInvoiceStatusColor = (status: Project['invoiceStatus']) => {
    switch (status) {
      case 'مبدئية':
        return 'bg-yellow-100 text-yellow-800';
      case 'تصنيع':
        return 'bg-orange-100 text-orange-800';
      case 'مكتملة':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getNextInvoiceStatus = (current: Project['invoiceStatus']): Project['invoiceStatus'] | null => {
    switch (current) {
      case 'مبدئية':
        return 'تصنيع';
      case 'تصنيع':
        return 'مكتملة';
      default:
        return null;
    }
  };

  const getNextProjectStatus = (current: Project['status']): Project['status'] | null => {
    switch (current) {
      case 'قيد التنفيذ':
        return 'مكتمل';
      case 'متأخر':
        return 'قيد التنفيذ';
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المشاريع...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Factory className="h-5 w-5" />
            إدارة دورة حياة المشاريع ({projects.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {projects.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">لا توجد مشاريع محفوظة</p>
              <p className="text-sm text-gray-500 mt-2">
                استخدم حاسبة التكلفة المحسنة لإنشاء مشروع جديد
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {projects.map((project) => (
                <Card key={project.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">{project.customerName}</h3>
                          {project.customerPhone && (
                            <span className="text-sm text-gray-600">({project.customerPhone})</span>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div>
                            <p className="text-sm text-gray-600">نوع الأثاث</p>
                            <p className="font-medium">{project.furnitureType}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">المساحة</p>
                            <p className="font-medium">{project.area} م²</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">التكلفة الإجمالية</p>
                            <p className="font-medium text-green-600">{formatLibyanDinar(project.totalCost)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">المبلغ المتبقي</p>
                            <p className="font-medium text-orange-600">{formatLibyanDinar(project.remainingAmount)}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 mb-4">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(project.status)}
                            <Badge className={getStatusColor(project.status)}>
                              {project.status}
                            </Badge>
                          </div>
                          
                          <ArrowRight className="h-4 w-4 text-gray-400" />
                          
                          <Badge className={getInvoiceStatusColor(project.invoiceStatus)}>
                            فاتورة {project.invoiceStatus}
                          </Badge>
                        </div>

                        <div className="flex items-center gap-2">
                          {getNextProjectStatus(project.status) && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateProjectStatus(project.id, getNextProjectStatus(project.status)!)}
                            >
                              تحديث إلى {getNextProjectStatus(project.status)}
                            </Button>
                          )}
                          
                          {getNextInvoiceStatus(project.invoiceStatus) && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateInvoiceStatus(project.id, getNextInvoiceStatus(project.invoiceStatus)!)}
                            >
                              تحويل إلى فاتورة {getNextInvoiceStatus(project.invoiceStatus)}
                            </Button>
                          )}
                          
                          <Button size="sm" variant="ghost">
                            <Eye className="h-4 w-4 mr-1" />
                            عرض التفاصيل
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectLifecycleManager;
