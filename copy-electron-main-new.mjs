import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const sourcePath = path.join(__dirname, 'public', 'electron.js');
const destinationPath = path.join(__dirname, 'dist-electron-main', 'public', 'electron.js');

// Ensure the destination directory exists
const destinationDir = path.dirname(destinationPath);
if (!fs.existsSync(destinationDir)) {
    fs.mkdirSync(destinationDir, { recursive: true });
}

fs.copyFileSync(sourcePath, destinationPath);
console.log(Copied  to );

// نسخ ملف قاعدة البيانات
const dbSource = path.join(__dirname, 'public', 'database-electron.cjs');
const dbDest = path.join(__dirname, 'dist-electron-main', 'public', 'database-electron.cjs');
fs.copyFileSync(dbSource, dbDest);
console.log(Copied  to );
