
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Plus, Edit, Trash2, Package, Hammer, Factory, Pen, Layers, Bell, AlertTriangle, CheckCircle } from "lucide-react";
import { Link } from "react-router-dom";

import EnhancedNavbar from "@/components/common/EnhancedNavbar";
import PageHeader from "@/components/common/PageHeader";
import DataTable, { TableColumn, TableAction } from "@/components/common/DataTable";
import FormField from "@/components/common/FormField";
import { LoadingState, ErrorState, DataState } from "@/components/common/LoadingState";

import { formatLibyanDinar } from "@/utils/calculations";
import { handleError, handleSuccess, validateRequired, validateNumber } from "@/utils/commonUtils";
import { themeClasses } from "@/styles/theme";
import { cn } from "@/lib/utils";

import {
  getWorkers,
  saveWorkers,
  saveWorker,
  getFactories,
  saveFactories,
  saveFactory,
  getDesigners,
  saveDesigners,
  saveDesigner,
  Worker,
  Factory as FactoryType,
  Designer
} from "@/utils/dataManager";
import { NotificationAlert } from "@/types/integration";
import DetailedMaterialsManager from "@/components/DetailedMaterialsManager";
import NotificationService from "@/services/notificationService";

const Materials = () => {
  const { toast } = useToast();

  // البيانات
  const [workers, setWorkers] = useState<Worker[]>([]);
  const [factories, setFactories] = useState<FactoryType[]>([]);
  const [designers, setDesigners] = useState<Designer[]>([]);
  const [notifications, setNotifications] = useState<NotificationAlert[]>([]);

  // حالات النماذج
  const [isAddingWorker, setIsAddingWorker] = useState(false);
  const [isAddingFactory, setIsAddingFactory] = useState(false);
  const [isAddingDesigner, setIsAddingDesigner] = useState(false);

  // معرفات التعديل
  const [editingWorkerId, setEditingWorkerId] = useState<string | null>(null);
  const [editingFactoryId, setEditingFactoryId] = useState<string | null>(null);
  const [editingDesignerId, setEditingDesignerId] = useState<string | null>(null);

  // بيانات النماذج
  
  const [workerForm, setWorkerForm] = useState({
    name: "", specialty: "", pricePerSqm: "", phone: ""
  });
  
  const [factoryForm, setFactoryForm] = useState({
    name: "", specialty: "", pricePerSqm: "", location: ""
  });
  
  const [designerForm, setDesignerForm] = useState({
    name: "", specialty: "", pricePerSqm: "", phone: ""
  });

  useEffect(() => {
    loadAllData();
    initializeNotifications();
  }, []);

  const loadAllData = async () => {
    try {
      const [workersData, factoriesData, designersData] = await Promise.all([
        getWorkers(),
        getFactories(),
        getDesigners()
      ]);

      setWorkers(workersData);
      setFactories(factoriesData);
      setDesigners(designersData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      // تعيين مصفوفات فارغة في حالة الخطأ
      setWorkers([]);
      setFactories([]);
      setDesigners([]);
    }
  };

  const initializeNotifications = async () => {
    try {
      await NotificationService.initialize();

      // تحميل التنبيهات المتعلقة بالمواد
      const materialNotifications = NotificationService.getNotifications({
        source: 'materials_system',
        unreadOnly: true,
        limit: 10
      });
      setNotifications(materialNotifications);

      // إضافة مستمع للتنبيهات الجديدة
      NotificationService.addListener((newNotifications) => {
        const materialNotifications = newNotifications.filter(n =>
          n.source === 'materials_system' && !n.read
        );
        setNotifications(materialNotifications);
      });

      // تشغيل فحص تنبيهات المواد
      await NotificationService.checkMaterialAlerts();
    } catch (error) {
      console.error('خطأ في تهيئة التنبيهات:', error);
    }
  };



  // وظائف إدارة العمال
  const handleSubmitWorker = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!workerForm.name || !workerForm.pricePerSqm) return;

    if (editingWorkerId) {
      // تحديث عامل موجود - سيتم تنفيذه لاحقاً
      console.log('تحديث العامل - سيتم تنفيذه لاحقاً');
      toast({
        title: "تحديث العامل",
        description: "سيتم تنفيذ التحديث لاحقاً",
        variant: "destructive"
      });
    } else {
      // إضافة عامل جديد
      const newWorker = {
        name: workerForm.name,
        specialty: workerForm.specialty,
        pricePerSqm: parseFloat(workerForm.pricePerSqm),
        phone: workerForm.phone
      };

      try {
        const id = await saveWorker(newWorker);
        if (id) {
          await loadAllData(); // إعادة تحميل البيانات
          resetWorkerForm();
          toast({
            title: "تم إضافة العامل",
            description: "تم حفظ البيانات بنجاح"
          });
        } else {
          throw new Error('فشل في حفظ العامل');
        }
      } catch (error) {
        console.error('خطأ في حفظ العامل:', error);
        toast({
          title: "خطأ في الحفظ",
          description: "حدث خطأ أثناء حفظ العامل",
          variant: "destructive"
        });
      }
    }
  };

  const resetWorkerForm = () => {
    setWorkerForm({ name: "", specialty: "", pricePerSqm: "", phone: "" });
    setIsAddingWorker(false);
    setEditingWorkerId(null);
  };

  // وظائف إدارة المصانع
  const handleSubmitFactory = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!factoryForm.name || !factoryForm.pricePerSqm) return;

    if (editingFactoryId) {
      // تحديث مصنع موجود - سيتم تنفيذه لاحقاً
      console.log('تحديث المصنع - سيتم تنفيذه لاحقاً');
      toast({
        title: "تحديث المصنع",
        description: "سيتم تنفيذ التحديث لاحقاً",
        variant: "destructive"
      });
    } else {
      // إضافة مصنع جديد
      const newFactory = {
        name: factoryForm.name,
        specialty: factoryForm.specialty,
        pricePerSqm: parseFloat(factoryForm.pricePerSqm),
        location: factoryForm.location
      };

      try {
        const id = await saveFactory(newFactory);
        if (id) {
          await loadAllData(); // إعادة تحميل البيانات
          resetFactoryForm();
          toast({
            title: "تم إضافة المصنع",
            description: "تم حفظ البيانات بنجاح"
          });
        } else {
          throw new Error('فشل في حفظ المصنع');
        }
      } catch (error) {
        console.error('خطأ في حفظ المصنع:', error);
        toast({
          title: "خطأ في الحفظ",
          description: "حدث خطأ أثناء حفظ المصنع",
          variant: "destructive"
        });
      }
    }
  };

  const resetFactoryForm = () => {
    setFactoryForm({ name: "", specialty: "", pricePerSqm: "", location: "" });
    setIsAddingFactory(false);
    setEditingFactoryId(null);
  };

  // وظائف إدارة المصممين
  const handleSubmitDesigner = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!designerForm.name || !designerForm.pricePerSqm) return;

    if (editingDesignerId) {
      // تحديث مصمم موجود - سيتم تنفيذه لاحقاً
      console.log('تحديث المصمم - سيتم تنفيذه لاحقاً');
      toast({
        title: "تحديث المصمم",
        description: "سيتم تنفيذ التحديث لاحقاً",
        variant: "destructive"
      });
    } else {
      // إضافة مصمم جديد
      const newDesigner = {
        name: designerForm.name,
        specialty: designerForm.specialty,
        pricePerSqm: parseFloat(designerForm.pricePerSqm),
        phone: designerForm.phone
      };

      try {
        const id = await saveDesigner(newDesigner);
        if (id) {
          await loadAllData(); // إعادة تحميل البيانات
          resetDesignerForm();
          toast({
            title: "تم إضافة المصمم",
            description: "تم حفظ البيانات بنجاح"
          });
        } else {
          throw new Error('فشل في حفظ المصمم');
        }
      } catch (error) {
        console.error('خطأ في حفظ المصمم:', error);
        toast({
          title: "خطأ في الحفظ",
          description: "حدث خطأ أثناء حفظ المصمم",
          variant: "destructive"
        });
      }
    }
  };

  const resetDesignerForm = () => {
    setDesignerForm({ name: "", specialty: "", pricePerSqm: "", phone: "" });
    setIsAddingDesigner(false);
    setEditingDesignerId(null);
  };

  return (
    <div className={themeClasses.pageBackground}>
      <EnhancedNavbar />

      <PageHeader
        title="إدارة موارد المصنع"
        description="إدارة شاملة للمواد والعمال والمصانع والمصممين"
        icon={Package}
        showBackButton
        backTo="/"
        gradient="primary"
      />

      <div className={themeClasses.container}>

        {/* تنبيهات المواد */}
        {notifications.length > 0 && (
          <Card className="shadow-md border-orange-200 bg-orange-50 mb-6">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-md font-semibold flex items-center gap-2 text-orange-700">
                <Bell className="h-4 w-4" />
                تنبيهات المواد ({notifications.length})
              </CardTitle>
              <Button
                size="sm"
                variant="outline"
                onClick={async () => {
                  await Promise.all(notifications.map(n => NotificationService.markAsRead(n.id)));
                  setNotifications([]);
                }}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                تحديد الكل كمقروء
              </Button>
            </CardHeader>
            <CardContent className="space-y-2">
              {notifications.slice(0, 5).map((notification) => (
                <div key={notification.id} className="flex items-start gap-2 p-3 bg-white rounded border">
                  <AlertTriangle className={`h-4 w-4 mt-0.5 ${
                    notification.priority === 'critical' ? 'text-red-500' :
                    notification.priority === 'high' ? 'text-orange-500' :
                    'text-yellow-500'
                  }`} />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{notification.title}</p>
                    <p className="text-xs text-gray-600">{notification.message}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      {new Date(notification.timestamp).toLocaleString('ar-LY')}
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={async () => {
                      await NotificationService.markAsRead(notification.id);
                      setNotifications(prev => prev.filter(n => n.id !== notification.id));
                    }}
                  >
                    ✕
                  </Button>
                </div>
              ))}
              {notifications.length > 5 && (
                <p className="text-xs text-gray-500 text-center">
                  و {notifications.length - 5} تنبيهات أخرى...
                </p>
              )}
            </CardContent>
          </Card>
        )}

        <Tabs defaultValue="detailed-materials" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="detailed-materials" className="flex items-center gap-2">
              <Layers className="h-4 w-4" />
              المواد والخامات
            </TabsTrigger>
            <TabsTrigger value="workers" className="flex items-center gap-2">
              <Hammer className="h-4 w-4" />
              العمال
            </TabsTrigger>
            <TabsTrigger value="factories" className="flex items-center gap-2">
              <Factory className="h-4 w-4" />
              المصانع
            </TabsTrigger>
            <TabsTrigger value="designers" className="flex items-center gap-2">
              <Pen className="h-4 w-4" />
              المصممين
            </TabsTrigger>
          </TabsList>

          {/* تبويبة المواد والخامات */}
          <TabsContent value="detailed-materials" className="space-y-6">
            <DetailedMaterialsManager />
          </TabsContent>

          {/* باقي التبويبات... */}
          <TabsContent value="workers" className="space-y-6">
            {/* نماذج وعرض العمال */}
            
            {/* Workers Tab Content */}
            {!isAddingWorker && (
              <div className="flex justify-end">
                <Button onClick={() => setIsAddingWorker(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة عامل جديد
                </Button>
              </div>
            )}

            {isAddingWorker && (
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                  <CardTitle>{editingWorkerId ? "تعديل العامل" : "إضافة عامل جديد"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmitWorker} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="workerName">اسم العامل</Label>
                        <Input
                          id="workerName"
                          value={workerForm.name}
                          onChange={(e) => setWorkerForm({...workerForm, name: e.target.value})}
                          placeholder="مثال: محمد أحمد"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="workerSpecialty">التخصص</Label>
                        <Input
                          id="workerSpecialty"
                          value={workerForm.specialty}
                          onChange={(e) => setWorkerForm({...workerForm, specialty: e.target.value})}
                          placeholder="مثال: نجار، دهان"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="workerPhone">رقم الهاتف</Label>
                      <Input
                        id="workerPhone"
                        type="tel"
                        value={workerForm.phone}
                        onChange={(e) => setWorkerForm({...workerForm, phone: e.target.value})}
                        placeholder="09X-XXXXXXX"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="workerPrice">الأجر لكل متر مربع (د.ل)</Label>
                      <Input
                        id="workerPrice"
                        type="number"
                        value={workerForm.pricePerSqm}
                        onChange={(e) => setWorkerForm({...workerForm, pricePerSqm: e.target.value})}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button type="button" variant="outline" onClick={resetWorkerForm}>إلغاء</Button>
                      <Button type="submit">{editingWorkerId ? "حفظ التعديلات" : "إضافة عامل"}</Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {workers.map((worker) => (
                <Card key={worker.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg text-gray-800">{worker.name}</CardTitle>
                        <span className="text-sm text-blue-600 font-medium">{worker.specialty}</span>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => {
                          setWorkerForm({
                            name: worker.name,
                            specialty: worker.specialty,
                            pricePerSqm: worker.pricePerSqm.toString(),
                            phone: worker.phone || ""
                          });
                          setEditingWorkerId(worker.id);
                          setIsAddingWorker(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          const updated = workers.filter(w => w.id !== worker.id);
                          setWorkers(updated);
                          saveWorkers(updated);
                          toast({ title: "تم حذف العامل", description: "تم حذف العامل بنجاح" });
                        }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {worker.phone && <p className="text-gray-600 text-sm mb-3">هاتف: {worker.phone}</p>}
                    <div className="text-2xl font-bold text-green-600">{formatLibyanDinar(worker.pricePerSqm)}/م²</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          
          </TabsContent>

          <TabsContent value="factories" className="space-y-6">
            {/* نماذج وعرض المصانع */}
            
            {/* Factories Tab Content */}
            {!isAddingFactory && (
              <div className="flex justify-end">
                <Button onClick={() => setIsAddingFactory(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مصنع جديد
                </Button>
              </div>
            )}

            {isAddingFactory && (
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                  <CardTitle>{editingFactoryId ? "تعديل المصنع" : "إضافة مصنع جديد"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmitFactory} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="factoryName">اسم المصنع</Label>
                        <Input
                          id="factoryName"
                          value={factoryForm.name}
                          onChange={(e) => setFactoryForm({...factoryForm, name: e.target.value})}
                          placeholder="مثال: مصنع الأثاث الحديث"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="factorySpecialty">التخصص</Label>
                        <Input
                          id="factorySpecialty"
                          value={factoryForm.specialty}
                          onChange={(e) => setFactoryForm({...factoryForm, specialty: e.target.value})}
                          placeholder="مثال: أثاث مكتبي، غرف نوم"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="factoryLocation">الموقع</Label>
                      <Input
                        id="factoryLocation"
                        value={factoryForm.location}
                        onChange={(e) => setFactoryForm({...factoryForm, location: e.target.value})}
                        placeholder="مثال: طرابلس"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="factoryPrice">التكلفة لكل متر مربع (د.ل)</Label>
                      <Input
                        id="factoryPrice"
                        type="number"
                        value={factoryForm.pricePerSqm}
                        onChange={(e) => setFactoryForm({...factoryForm, pricePerSqm: e.target.value})}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button type="button" variant="outline" onClick={resetFactoryForm}>إلغاء</Button>
                      <Button type="submit">{editingFactoryId ? "حفظ التعديلات" : "إضافة مصنع"}</Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {factories.map((factory) => (
                <Card key={factory.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg text-gray-800">{factory.name}</CardTitle>
                        <span className="text-sm text-blue-600 font-medium">{factory.specialty}</span>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => {
                          setFactoryForm({
                            name: factory.name,
                            specialty: factory.specialty,
                            pricePerSqm: factory.pricePerSqm.toString(),
                            location: factory.location || ""
                          });
                          setEditingFactoryId(factory.id);
                          setIsAddingFactory(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          const updated = factories.filter(f => f.id !== factory.id);
                          setFactories(updated);
                          saveFactories(updated);
                          toast({ title: "تم حذف المصنع", description: "تم حذف المصنع بنجاح" });
                        }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {factory.location && <p className="text-gray-600 text-sm mb-3">الموقع: {factory.location}</p>}
                    <div className="text-2xl font-bold text-green-600">{formatLibyanDinar(factory.pricePerSqm)}/م²</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          
          </TabsContent>

          <TabsContent value="designers" className="space-y-6">
            {/* نماذج وعرض المصممين */}
            
            {/* Designers Tab Content */}
            {!isAddingDesigner && (
              <div className="flex justify-end">
                <Button onClick={() => setIsAddingDesigner(true)} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة مصمم جديد
                </Button>
              </div>
            )}

            {isAddingDesigner && (
              <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
                  <CardTitle>{editingDesignerId ? "تعديل المصمم" : "إضافة مصمم جديد"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <form onSubmit={handleSubmitDesigner} className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="designerName">اسم المصمم</Label>
                        <Input
                          id="designerName"
                          value={designerForm.name}
                          onChange={(e) => setDesignerForm({...designerForm, name: e.target.value})}
                          placeholder="مثال: فاطمة محمد"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="designerSpecialty">التخصص</Label>
                        <Input
                          id="designerSpecialty"
                          value={designerForm.specialty}
                          onChange={(e) => setDesignerForm({...designerForm, specialty: e.target.value})}
                          placeholder="مثال: تصميم داخلي، تصميم خارجي"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="designerPhone">رقم الهاتف</Label>
                      <Input
                        id="designerPhone"
                        type="tel"
                        value={designerForm.phone}
                        onChange={(e) => setDesignerForm({...designerForm, phone: e.target.value})}
                        placeholder="09X-XXXXXXX"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="designerPrice">الأتعاب لكل متر مربع (د.ل)</Label>
                      <Input
                        id="designerPrice"
                        type="number"
                        value={designerForm.pricePerSqm}
                        onChange={(e) => setDesignerForm({...designerForm, pricePerSqm: e.target.value})}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                    <div className="flex gap-2 justify-end">
                      <Button type="button" variant="outline" onClick={resetDesignerForm}>إلغاء</Button>
                      <Button type="submit">{editingDesignerId ? "حفظ التعديلات" : "إضافة مصمم"}</Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            )}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {designers.map((designer) => (
                <Card key={designer.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg text-gray-800">{designer.name}</CardTitle>
                        <span className="text-sm text-blue-600 font-medium">{designer.specialty}</span>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => {
                          setDesignerForm({
                            name: designer.name,
                            specialty: designer.specialty,
                            pricePerSqm: designer.pricePerSqm.toString(),
                            phone: designer.phone || ""
                          });
                          setEditingDesignerId(designer.id);
                          setIsAddingDesigner(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => {
                          const updated = designers.filter(d => d.id !== designer.id);
                          setDesigners(updated);
                          saveDesigners(updated);
                          toast({ title: "تم حذف المصمم", description: "تم حذف المصمم بنجاح" });
                        }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {designer.phone && <p className="text-gray-600 text-sm mb-3">هاتف: {designer.phone}</p>}
                    <div className="text-2xl font-bold text-green-600">{formatLibyanDinar(designer.pricePerSqm)}/م²</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Materials;
