# تعليمات اختبار تطبيق إدارة مصنع الأثاث المكتبي

## خطوات الاختبار الشامل

### 1. اختبار تشغيل التطبيق

```bash
# تشغيل التطبيق
npm run electron:dev
```

**النتائج المتوقعة:**
- ✅ يجب أن يظهر في الكونسول: "تم الاتصال بقاعدة البيانات بنجاح"
- ✅ يجب أن يظهر: "تم إنشاء جميع الجداول بنجاح"
- ✅ يجب أن يظهر: "تم إدراج البيانات الافتراضية للاختبار"
- ✅ يجب أن يفتح التطبيق بنجاح

### 2. اختبار حاسبة التكلفة (إضافة مشروع جديد)

**الخطوات:**
1. افت<PERSON> التطبيق
2. في الصفحة الرئيسية، ستجد حاسبة التكلفة
3. املأ البيانات التالية:
   - اسم العميل: "أحمد محمد"
   - رقم الهاتف: "0912345678"
   - المساحة: 50
   - نوع الأثاث: "مكاتب إدارية"
4. اختر من القوائم المنسدلة:
   - المادة: "خشب MDF"
   - العامل: "أحمد محمد"
   - المصنع: "مصنع الأثاث الحديث"
   - المصمم: "سارة أحمد"
5. أدخل الدفعة المقدمة: 1000
6. اضغط "حفظ المشروع"

**النتائج المتوقعة:**
- ✅ يجب أن تظهر رسالة "تم حفظ المشروع"
- ✅ يجب أن تُمسح البيانات من النموذج
- ✅ يجب أن تتحدث الإحصائيات في الصفحة الرئيسية

### 3. اختبار صفحة التقارير

**الخطوات:**
1. اذهب إلى صفحة "التقارير والمشاريع"
2. انتظر تحميل البيانات

**النتائج المتوقعة:**
- ✅ يجب أن تظهر مؤشر التحميل أولاً
- ✅ يجب أن يظهر المشروع الذي أضفته في الخطوة السابقة
- ✅ يجب أن تظهر الإحصائيات الصحيحة (إجمالي الإيرادات، المبالغ المحصلة، إلخ)
- ✅ يجب أن تعمل فلاتر الحالة والشهر

### 4. اختبار تحديث حالة المشروع

**الخطوات:**
1. في صفحة التقارير، ابحث عن المشروع الذي أضفته
2. غيّر حالة المشروع من "قيد التنفيذ" إلى "مكتمل"

**النتائج المتوقعة:**
- ✅ يجب أن تظهر رسالة "تم تحديث حالة المشروع"
- ✅ يجب أن تتحدث حالة المشروع في الجدول

### 5. اختبار صفحة الخزينة

**الخطوات:**
1. اذهب إلى صفحة "إدارة الخزينة"
2. انتظر تحميل البيانات
3. اذهب إلى تبويب "إضافة معاملة"
4. املأ البيانات التالية:
   - نوع المعاملة: "دخل"
   - فئة المعاملة: "دفعة مشروع"
   - المبلغ: 500
   - الوصف: "دفعة إضافية من مشروع أحمد محمد"
5. اضغط "إضافة المعاملة"

**النتائج المتوقعة:**
- ✅ يجب أن تظهر رسالة "تم إضافة المعاملة إلى الخزينة"
- ✅ يجب أن تتحدث الإحصائيات في أعلى الصفحة
- ✅ يجب أن تظهر المعاملة في جدول المعاملات

### 6. اختبار الأزرار السريعة في الخزينة

**الخطوات:**
1. في صفحة الخزينة، تبويب "إضافة معاملة"
2. اضغط على "دفعة مشروع متبقية"

**النتائج المتوقعة:**
- ✅ يجب أن تُملأ البيانات تلقائياً بناءً على المشاريع الموجودة
- ✅ يجب أن يظهر اسم العميل والمبلغ المتبقي

### 7. اختبار صفحة إدارة المواد

**الخطوات:**
1. اذهب إلى صفحة "إدارة المواد والمصانع"
2. تحقق من وجود البيانات الافتراضية:
   - المواد: خشب MDF، خشب طبيعي، معدن
   - العمال: أحمد محمد، محمد علي
   - المصانع: مصنع الأثاث الحديث، مصنع الخشب الفاخر
   - المصممين: سارة أحمد، خالد محمود

**النتائج المتوقعة:**
- ✅ يجب أن تظهر جميع البيانات الافتراضية
- ✅ يجب أن تعمل وظائف الإضافة والتعديل

### 8. اختبار إعادة تشغيل التطبيق

**الخطوات:**
1. أغلق التطبيق
2. أعد تشغيله
3. تحقق من وجود البيانات التي أدخلتها

**النتائج المتوقعة:**
- ✅ يجب أن تظهر جميع البيانات التي أدخلتها سابقاً
- ✅ يجب أن تظهر المشاريع في صفحة التقارير
- ✅ يجب أن تظهر المعاملات المالية في صفحة الخزينة

## اختبارات إضافية

### اختبار تصدير التقارير
1. في صفحة التقارير، اضغط "تحميل تقرير مفصل"
2. تحقق من تحميل ملف نصي يحتوي على تفاصيل المشاريع

### اختبار تصدير بيانات الخزينة
1. في صفحة الخزينة، اضغط "تصدير"
2. تحقق من تحميل ملف نصي يحتوي على تفاصيل المعاملات

## مؤشرات النجاح

### ✅ النظام يعمل بشكل صحيح إذا:
- تم حفظ جميع البيانات في قاعدة البيانات
- البيانات تظهر في جميع الأقسام المناسبة
- البيانات تبقى محفوظة بعد إعادة تشغيل التطبيق
- الإحصائيات تتحدث تلقائياً عند إضافة بيانات جديدة
- رسائل النجاح والخطأ تظهر بشكل مناسب

### ❌ مؤشرات وجود مشاكل:
- البيانات لا تُحفظ أو تختفي بعد إعادة التشغيل
- رسائل خطأ في الكونسول
- البيانات لا تظهر في الأقسام المختلفة
- الإحصائيات لا تتحدث

## ملاحظات مهمة

1. **مسار قاعدة البيانات:** تُحفظ قاعدة البيانات في:
   ```
   C:\Users\<USER>\AppData\Roaming\vite_react_shadcn_ts\furniture_factory.db
   ```

2. **البيانات الافتراضية:** تُضاف تلقائياً عند أول تشغيل فقط

3. **الكونسول:** راقب رسائل الكونسول للتأكد من عدم وجود أخطاء

4. **الأداء:** قد تستغرق العمليات وقتاً قصيراً لتحميل البيانات من قاعدة البيانات

## في حالة وجود مشاكل

1. تحقق من رسائل الكونسول
2. تأكد من وجود ملف قاعدة البيانات
3. أعد تشغيل التطبيق
4. تحقق من صحة البيانات المدخلة
