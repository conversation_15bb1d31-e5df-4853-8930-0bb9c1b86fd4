# 🏭 نظام إدارة مصنع الأثاث - تطبيق سطح المكتب

## ✅ تم تجهيز التطبيق بنجاح!

تم تحويل تطبيق الويب إلى تطبيق سطح مكتب باستخدام Electron مع جميع الميزات المطلوبة.

## 🚀 طرق التشغيل

### الطريقة الأولى: تشغيل سريع (Windows)
```
انقر نقراً مزدوجاً على ملف: start.bat
```

### الطريقة الثانية: تشغيل سريع (Mac/Linux)
```bash
./start.sh
```

### الطريقة الثالثة: تشغيل مباشر (بدون خادم)
```bash
# 1. تثبيت التبعيات
npm install

# 2. بناء التطبيق
npm run build

# 3. تشغيل التطبيق مباشرة
npm run electron
```

### الطريقة الرابعة: تشغيل مع خادم التطوير
```bash
# 1. تثبيت التبعيات
npm install

# 2. تشغيل التطبيق مع خادم التطوير
npm run electron:dev
```

## 📦 إنشاء ملف التثبيت

لإنشاء ملف تثبيت للتوزيع:

```bash
# طريقة سهلة
npm run build:desktop

# أو طريقة تفصيلية
npm run build
npm run electron:dist
```

## 📁 ملفات التثبيت المُنتجة

ستجد ملفات التثبيت في مجلد `dist-electron/`:

- **Windows**: `نظام إدارة مصنع الأثاث-1.0.0-Setup.exe`
- **macOS**: `نظام إدارة مصنع الأثاث-1.0.0.dmg`
- **Linux**: `نظام إدارة مصنع الأثاث-1.0.0.AppImage`

## 🎯 الميزات الجديدة في تطبيق سطح المكتب

### 📋 قائمة التطبيق العربية
- قائمة "ملف" مع خيارات الحفظ والفتح
- قائمة "تحرير" مع عمليات النسخ واللصق
- قائمة "عرض" مع خيارات التكبير والتصغير
- قائمة "نافذة" لإدارة النوافذ
- قائمة "مساعدة" مع معلومات التطبيق

### ⌨️ اختصارات لوحة المفاتيح
- `Ctrl+N` - جديد
- `Ctrl+O` - فتح
- `Ctrl+S` - حفظ
- `Ctrl+Q` - خروج
- `F11` - ملء الشاشة
- `F12` - أدوات المطور

### 🔒 الأمان
- تم تعطيل Node.js integration
- تم تفعيل Context Isolation
- استخدام preload script آمن
- منع فتح نوافذ خارجية

### 🎨 واجهة محسنة
- نافذة مخصصة بحجم مناسب (1400x900)
- حد أدنى للحجم (1200x800)
- أيقونة مخصصة للتطبيق
- شريط عنوان عربي

## 🔧 استكشاف الأخطاء

### مشكلة: التطبيق لا يعمل
```bash
npm run clean
npm install
npm run electron:dev
```

### مشكلة: فشل في البناء
```bash
# تحقق من وجود الملفات المطلوبة
dir public\electron.js
dir public\preload.js

# أعد البناء
npm run build
npm run electron:pack
```

### مشكلة: ملف التثبيت لا يعمل
```bash
# تأكد من بناء React أولاً
npm run build

# ثم بناء Electron
npm run electron:dist
```

## 📋 الملفات المضافة

تم إضافة الملفات التالية للمشروع:

```
📁 public/
├── electron.js          # ملف Electron الرئيسي
├── preload.js          # ملف الأمان
└── icon.png            # أيقونة التطبيق

📁 المجلد الرئيسي/
├── ELECTRON_README.md   # دليل Electron مفصل
├── QUICK_START.md      # دليل البدء السريع
├── تعليمات_التشغيل.md  # هذا الملف
├── build-electron.js   # سكريبت البناء
├── electron-builder.json # إعدادات البناء
├── start.bat          # تشغيل سريع (Windows)
└── start.sh           # تشغيل سريع (Mac/Linux)
```

## 🎉 مبروك!

تطبيقك جاهز الآن للعمل كتطبيق سطح مكتب مستقل مع جميع الميزات التالية:

✅ واجهة عربية كاملة
✅ حاسبة التكلفة
✅ إدارة المواد والموارد
✅ إدارة الخزينة
✅ التقارير والمشاريع
✅ إدارة المرتبات
✅ إدارة العملاء والفواتير
✅ تخزين البيانات محلياً
✅ قوائم وأختصارات عربية
✅ أمان محسن

## 📞 الدعم

إذا واجهت أي مشاكل، تأكد من:
1. تثبيت Node.js (الإصدار 16 أو أحدث)
2. تشغيل `npm install` أولاً
3. استخدام `npm run clean` لحل المشاكل
