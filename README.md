# نظام إدارة مصنع الأثاث

نظام شامل لإدارة مصنع الأثاث والتكاليف مبني بتقنيات حديثة.

## المميزات

- 🧮 حاسبة التكلفة الذكية
- 📦 إدارة المواد والمخزون
- 👥 إدارة العمال والموظفين
- 🏭 إدارة المصانع والمصممين
- 💰 إدارة الخزينة والمالية
- 📊 التقارير والإحصائيات
- 🖨️ نظام الطباعة المتقدم
- 🔔 نظام التنبيهات

## التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript
- **UI**: Tailwind CSS + shadcn/ui
- **Desktop**: Electron
- **Database**: SQLite
- **Build**: Vite
- **State Management**: React Query + Custom State Manager

## التشغيل

### متطلبات النظام
- Node.js 18+ 
- npm أو yarn

### التثبيت والتشغيل
```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm run electron:dev

# بناء التطبيق للإنتاج
npm run build

# إنشاء ملف التثبيت
npm run electron:dist
```

## البنية

```
src/
├── components/     # المكونات القابلة لإعادة الاستخدام
├── pages/         # صفحات التطبيق
├── services/      # خدمات البيانات والAPI
├── utils/         # الوظائف المساعدة
├── types/         # تعريفات TypeScript
├── config/        # إعدادات التطبيق
└── hooks/         # React Hooks مخصصة
```

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
