// خدمة التنبيهات والإشعارات
import { NotificationAlert } from '@/types/integration';

// التحقق من وجود Electron API
const isElectron = typeof window !== 'undefined' && window.electronAPI;

export class NotificationService {
  private static listeners: ((notifications: NotificationAlert[]) => void)[] = [];
  private static currentNotifications: NotificationAlert[] = [];

  // تهيئة خدمة التنبيهات
  static async initialize(): Promise<void> {
    try {
      await this.createNotificationTable();
      await this.loadNotifications();
      this.startNotificationPolling();
    } catch (error) {
      console.error('خطأ في تهيئة خدمة التنبيهات:', error);
    }
  }

  // إنشاء جدول التنبيهات
  private static async createNotificationTable(): Promise<void> {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        source TEXT NOT NULL,
        priority TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        read INTEGER DEFAULT 0,
        action_required INTEGER DEFAULT 0,
        related_id TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `;

    if (isElectron) {
      await window.electronAPI.runQuery(createTableQuery, []);
    }
  }

  // تحميل التنبيهات من قاعدة البيانات
  private static async loadNotifications(): Promise<void> {
    try {
      if (isElectron) {
        const notifications = await window.electronAPI.allQuery(
          'SELECT * FROM notifications ORDER BY timestamp DESC LIMIT 100',
          []
        );
        
        this.currentNotifications = notifications.map((n: any) => ({
          id: n.id,
          type: n.type,
          title: n.title,
          message: n.message,
          source: n.source,
          priority: n.priority,
          timestamp: n.timestamp,
          read: Boolean(n.read),
          actionRequired: Boolean(n.action_required),
          relatedId: n.related_id
        }));

        this.notifyListeners();
      }
    } catch (error) {
      console.error('خطأ في تحميل التنبيهات:', error);
    }
  }

  // إضافة تنبيه جديد
  static async addNotification(notification: Omit<NotificationAlert, 'id' | 'timestamp' | 'read'>): Promise<string> {
    const newNotification: NotificationAlert = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      read: false,
      ...notification
    };

    try {
      if (isElectron) {
        await window.electronAPI.runQuery(
          `INSERT INTO notifications 
           (id, type, title, message, source, priority, timestamp, read, action_required, related_id) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            newNotification.id,
            newNotification.type,
            newNotification.title,
            newNotification.message,
            newNotification.source,
            newNotification.priority,
            newNotification.timestamp,
            0,
            newNotification.actionRequired ? 1 : 0,
            newNotification.relatedId || null
          ]
        );
      }

      this.currentNotifications.unshift(newNotification);
      this.notifyListeners();

      // إرسال إشعار للنظام إذا كان ذو أولوية عالية
      if (newNotification.priority === 'critical' || newNotification.priority === 'high') {
        this.showSystemNotification(newNotification);
      }

      return newNotification.id;
    } catch (error) {
      console.error('خطأ في إضافة التنبيه:', error);
      throw error;
    }
  }

  // تحديث حالة قراءة التنبيه
  static async markAsRead(notificationId: string): Promise<void> {
    try {
      if (isElectron) {
        await window.electronAPI.runQuery(
          'UPDATE notifications SET read = 1 WHERE id = ?',
          [notificationId]
        );
      }

      const notification = this.currentNotifications.find(n => n.id === notificationId);
      if (notification) {
        notification.read = true;
        this.notifyListeners();
      }
    } catch (error) {
      console.error('خطأ في تحديث حالة التنبيه:', error);
    }
  }

  // تحديث جميع التنبيهات كمقروءة
  static async markAllAsRead(): Promise<void> {
    try {
      if (isElectron) {
        await window.electronAPI.runQuery('UPDATE notifications SET read = 1', []);
      }

      this.currentNotifications.forEach(n => n.read = true);
      this.notifyListeners();
    } catch (error) {
      console.error('خطأ في تحديث جميع التنبيهات:', error);
    }
  }

  // حذف تنبيه
  static async deleteNotification(notificationId: string): Promise<void> {
    try {
      if (isElectron) {
        await window.electronAPI.runQuery(
          'DELETE FROM notifications WHERE id = ?',
          [notificationId]
        );
      }

      this.currentNotifications = this.currentNotifications.filter(n => n.id !== notificationId);
      this.notifyListeners();
    } catch (error) {
      console.error('خطأ في حذف التنبيه:', error);
    }
  }

  // الحصول على التنبيهات
  static getNotifications(filter?: {
    unreadOnly?: boolean;
    priority?: string;
    source?: string;
    limit?: number;
  }): NotificationAlert[] {
    let filtered = [...this.currentNotifications];

    if (filter) {
      if (filter.unreadOnly) {
        filtered = filtered.filter(n => !n.read);
      }
      if (filter.priority) {
        filtered = filtered.filter(n => n.priority === filter.priority);
      }
      if (filter.source) {
        filtered = filtered.filter(n => n.source === filter.source);
      }
      if (filter.limit) {
        filtered = filtered.slice(0, filter.limit);
      }
    }

    return filtered;
  }

  // الحصول على عدد التنبيهات غير المقروءة
  static getUnreadCount(): number {
    return this.currentNotifications.filter(n => !n.read).length;
  }

  // إضافة مستمع للتنبيهات
  static addListener(callback: (notifications: NotificationAlert[]) => void): void {
    this.listeners.push(callback);
  }

  // إزالة مستمع للتنبيهات
  static removeListener(callback: (notifications: NotificationAlert[]) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // إشعار المستمعين
  private static notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.currentNotifications);
      } catch (error) {
        console.error('خطأ في إشعار المستمع:', error);
      }
    });
  }

  // إرسال إشعار للنظام
  private static showSystemNotification(notification: NotificationAlert): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icon.png',
        tag: notification.id
      });
    } else if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/icon.png',
            tag: notification.id
          });
        }
      });
    }
  }

  // بدء استطلاع التنبيهات الجديدة
  private static startNotificationPolling(): void {
    setInterval(async () => {
      await this.checkForNewNotifications();
    }, 30000); // كل 30 ثانية
  }

  // التحقق من التنبيهات الجديدة
  private static async checkForNewNotifications(): Promise<void> {
    try {
      if (isElectron) {
        const lastTimestamp = this.currentNotifications.length > 0 
          ? this.currentNotifications[0].timestamp 
          : new Date(0).toISOString();

        const newNotifications = await window.electronAPI.allQuery(
          'SELECT * FROM notifications WHERE timestamp > ? ORDER BY timestamp DESC',
          [lastTimestamp]
        );

        if (newNotifications.length > 0) {
          const formattedNotifications = newNotifications.map((n: any) => ({
            id: n.id,
            type: n.type,
            title: n.title,
            message: n.message,
            source: n.source,
            priority: n.priority,
            timestamp: n.timestamp,
            read: Boolean(n.read),
            actionRequired: Boolean(n.action_required),
            relatedId: n.related_id
          }));

          this.currentNotifications = [...formattedNotifications, ...this.currentNotifications];
          this.notifyListeners();
        }
      }
    } catch (error) {
      console.error('خطأ في التحقق من التنبيهات الجديدة:', error);
    }
  }

  // إنشاء تنبيهات تلقائية للمواد
  static async checkMaterialAlerts(): Promise<void> {
    try {
      if (isElectron) {
        // التحقق من المواد التي تحتاج إعادة تموين
        const lowStockMaterials = await window.electronAPI.allQuery(
          'SELECT * FROM detailed_materials WHERE availableQuantity <= minQuantity AND isActive = 1',
          []
        );

        for (const material of lowStockMaterials) {
          await this.addNotification({
            type: 'warning',
            title: 'نقص في المخزون',
            message: `المادة "${material.name}" تحتاج إعادة تموين. الكمية الحالية: ${material.availableQuantity}، الحد الأدنى: ${material.minQuantity}`,
            source: 'materials_system',
            priority: 'high',
            actionRequired: true,
            relatedId: material.id
          });
        }

        // التحقق من المواد المنتهية الصلاحية (إذا كان هناك تاريخ انتهاء)
        const expiredMaterials = await window.electronAPI.allQuery(
          `SELECT * FROM detailed_materials 
           WHERE expiry_date IS NOT NULL 
           AND date(expiry_date) <= date('now', '+30 days') 
           AND isActive = 1`,
          []
        );

        for (const material of expiredMaterials) {
          await this.addNotification({
            type: 'error',
            title: 'مادة قاربت على الانتهاء',
            message: `المادة "${material.name}" ستنتهي صلاحيتها قريباً. تاريخ الانتهاء: ${material.expiry_date}`,
            source: 'materials_system',
            priority: 'critical',
            actionRequired: true,
            relatedId: material.id
          });
        }
      }
    } catch (error) {
      console.error('خطأ في التحقق من تنبيهات المواد:', error);
    }
  }

  // إنشاء تنبيهات للمدفوعات المتأخرة
  static async checkPaymentAlerts(): Promise<void> {
    try {
      if (isElectron) {
        const overdueProjects = await window.electronAPI.allQuery(
          `SELECT * FROM projects 
           WHERE remainingAmount > 0 
           AND date(createdAt) <= date('now', '-30 days')
           AND status != 'ملغي'`,
          []
        );

        for (const project of overdueProjects) {
          await this.addNotification({
            type: 'warning',
            title: 'دفعة متأخرة',
            message: `العميل "${project.customerName}" لديه مبلغ متبقي ${project.remainingAmount} د.ل منذ أكثر من 30 يوم`,
            source: 'treasury_system',
            priority: 'medium',
            actionRequired: true,
            relatedId: project.id
          });
        }
      }
    } catch (error) {
      console.error('خطأ في التحقق من تنبيهات المدفوعات:', error);
    }
  }

  // تشغيل جميع فحوصات التنبيهات
  static async runAllAlertChecks(): Promise<void> {
    await Promise.all([
      this.checkMaterialAlerts(),
      this.checkPaymentAlerts()
    ]);
  }
}

export default NotificationService;
