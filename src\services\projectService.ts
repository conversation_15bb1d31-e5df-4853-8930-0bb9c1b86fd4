// خدمة المشاريع الموحدة
// تهدف لتوحيد جميع عمليات إدارة المشاريع

import { runQuery, getQuery, allQuery } from '../utils/database';
import { Project } from '../utils/dataManager';
import { measureFunction } from './performanceMonitor';
import { handleError, ErrorType } from './errorHandler';
import { 
  ensureArray, 
  validateId, 
  validateProjectData,
  safeReduce 
} from '@/utils/dataValidation';
import { APP_CONFIG } from '@/config/appConfig';

export class ProjectService {
  // جلب جميع المشاريع
  static async getAll(): Promise<Project[]> {
    return measureFunction(async () => {
      try {
        const projects = await allQuery(`
          SELECT p.*, c.name as customerName, c.phone as customerPhone
          FROM projects p
          LEFT JOIN customers c ON p.customerId = c.id
          ORDER BY p.createdAt DESC
        `);
        return ensureArray(projects).map(validateProjectData);
      } catch (error) {
        handleError(error, 'جلب جميع المشاريع', ErrorType.DATABASE);
        return [];
      }
    }, 'Get All Projects', 'database');
  }

  // جلب مشروع بالمعرف
  static async getById(id: string): Promise<Project | null> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) return null;
        
        const project = await getQuery(`
          SELECT p.*, c.name as customerName, c.phone as customerPhone
          FROM projects p
          LEFT JOIN customers c ON p.customerId = c.id
          WHERE p.id = ?
        `, [validId]);
        
        return project ? validateProjectData(project) : null;
      } catch (error) {
        handleError(error, 'جلب مشروع بالمعرف', ErrorType.DATABASE);
        return null;
      }
    }, 'Get Project By ID', 'database', { id });
  }

  // إنشاء مشروع جديد
  static async create(project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    return measureFunction(async () => {
      try {
        const validatedProject = validateProjectData(project);
        const id = `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        await runQuery(`
          INSERT INTO projects (
            id, customerId, customerName, furnitureType, area, 
            materialId, workerId, factoryId, designerId,
            materialCost, workerCost, factoryCost, designerCost,
            totalCost, paidAmount, remainingAmount, status,
            notes, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          id,
          validatedProject.customerId,
          validatedProject.customerName,
          validatedProject.furnitureType,
          validatedProject.area,
          validatedProject.materialId,
          validatedProject.workerId,
          validatedProject.factoryId,
          validatedProject.designerId,
          validatedProject.materialCost,
          validatedProject.workerCost,
          validatedProject.factoryCost,
          validatedProject.designerCost,
          validatedProject.totalCost,
          validatedProject.paidAmount || 0,
          validatedProject.remainingAmount || validatedProject.totalCost,
          validatedProject.status || APP_CONFIG.projectStatuses[0],
          validatedProject.notes || '',
          new Date().toISOString(),
          new Date().toISOString()
        ]);
        
        return id;
      } catch (error) {
        handleError(error, 'إنشاء مشروع جديد', ErrorType.DATABASE);
        throw error;
      }
    }, 'Create Project', 'database');
  }

  // تحديث مشروع
  static async update(id: string, project: Partial<Project>): Promise<void> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) throw new Error('معرف المشروع غير صحيح');
        
        const validatedProject = validateProjectData(project);
        const fields = Object.keys(validatedProject).filter(
          key => key !== 'id' && key !== 'createdAt' && validatedProject[key as keyof Project] !== undefined
        );
        
        if (fields.length === 0) return;
        
        const values = fields.map(key => validatedProject[key as keyof Project]);
        const setClause = fields.map(field => `${field} = ?`).join(', ');
        
        await runQuery(
          `UPDATE projects SET ${setClause}, updatedAt = ? WHERE id = ?`,
          [...values, new Date().toISOString(), validId]
        );
      } catch (error) {
        handleError(error, 'تحديث المشروع', ErrorType.DATABASE);
        throw error;
      }
    }, 'Update Project', 'database', { id });
  }

  // حذف مشروع
  static async delete(id: string): Promise<void> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) throw new Error('معرف المشروع غير صحيح');
        
        // حذف المشروع والفواتير المرتبطة به
        await runQuery('DELETE FROM invoices WHERE projectId = ?', [validId]);
        await runQuery('DELETE FROM projects WHERE id = ?', [validId]);
      } catch (error) {
        handleError(error, 'حذف المشروع', ErrorType.DATABASE);
        throw error;
      }
    }, 'Delete Project', 'database', { id });
  }

  // جلب المشاريع حسب الحالة
  static async getByStatus(status: string): Promise<Project[]> {
    return measureFunction(async () => {
      try {
        const projects = await allQuery(`
          SELECT p.*, c.name as customerName, c.phone as customerPhone
          FROM projects p
          LEFT JOIN customers c ON p.customerId = c.id
          WHERE p.status = ?
          ORDER BY p.createdAt DESC
        `, [status]);
        
        return ensureArray(projects).map(validateProjectData);
      } catch (error) {
        handleError(error, 'جلب المشاريع حسب الحالة', ErrorType.DATABASE);
        return [];
      }
    }, 'Get Projects By Status', 'database', { status });
  }

  // جلب المشاريع المتأخرة
  static async getOverdueProjects(): Promise<Project[]> {
    return measureFunction(async () => {
      try {
        const overdueDate = new Date();
        overdueDate.setDate(overdueDate.getDate() - APP_CONFIG.notifications.overdueProjectDays);
        
        const projects = await allQuery(`
          SELECT p.*, c.name as customerName, c.phone as customerPhone
          FROM projects p
          LEFT JOIN customers c ON p.customerId = c.id
          WHERE p.status IN ('قيد التنفيذ', 'جديد') 
          AND p.createdAt < ?
          ORDER BY p.createdAt ASC
        `, [overdueDate.toISOString()]);
        
        return ensureArray(projects).map(validateProjectData);
      } catch (error) {
        handleError(error, 'جلب المشاريع المتأخرة', ErrorType.DATABASE);
        return [];
      }
    }, 'Get Overdue Projects', 'database');
  }

  // حساب إحصائيات المشاريع
  static async getStatistics(): Promise<{
    total: number;
    active: number;
    completed: number;
    overdue: number;
    totalRevenue: number;
    pendingPayments: number;
    averageProjectValue: number;
  }> {
    return measureFunction(async () => {
      try {
        const projects = await this.getAll();
        const overdueProjects = await this.getOverdueProjects();
        
        const stats = {
          total: projects.length,
          active: projects.filter(p => p.status === 'قيد التنفيذ').length,
          completed: projects.filter(p => p.status === 'مكتمل').length,
          overdue: overdueProjects.length,
          totalRevenue: safeReduce(projects, (sum, p) => sum + (p.paidAmount || 0), 0),
          pendingPayments: safeReduce(projects, (sum, p) => sum + (p.remainingAmount || 0), 0),
          averageProjectValue: 0
        };
        
        if (stats.total > 0) {
          const totalValue = safeReduce(projects, (sum, p) => sum + (p.totalCost || 0), 0);
          stats.averageProjectValue = totalValue / stats.total;
        }
        
        return stats;
      } catch (error) {
        handleError(error, 'حساب إحصائيات المشاريع', ErrorType.DATABASE);
        return {
          total: 0,
          active: 0,
          completed: 0,
          overdue: 0,
          totalRevenue: 0,
          pendingPayments: 0,
          averageProjectValue: 0
        };
      }
    }, 'Get Project Statistics', 'database');
  }

  // البحث في المشاريع
  static async search(query: string): Promise<Project[]> {
    return measureFunction(async () => {
      try {
        const searchTerm = `%${query.toLowerCase()}%`;
        const projects = await allQuery(`
          SELECT p.*, c.name as customerName, c.phone as customerPhone
          FROM projects p
          LEFT JOIN customers c ON p.customerId = c.id
          WHERE LOWER(p.customerName) LIKE ? 
          OR LOWER(p.furnitureType) LIKE ?
          OR LOWER(p.id) LIKE ?
          OR LOWER(c.name) LIKE ?
          ORDER BY p.createdAt DESC
        `, [searchTerm, searchTerm, searchTerm, searchTerm]);
        
        return ensureArray(projects).map(validateProjectData);
      } catch (error) {
        handleError(error, 'البحث في المشاريع', ErrorType.DATABASE);
        return [];
      }
    }, 'Search Projects', 'database', { query });
  }

  // تحديث حالة الدفع
  static async updatePayment(id: string, paidAmount: number): Promise<void> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) throw new Error('معرف المشروع غير صحيح');
        
        const project = await this.getById(validId);
        if (!project) throw new Error('المشروع غير موجود');
        
        const newPaidAmount = (project.paidAmount || 0) + paidAmount;
        const remainingAmount = (project.totalCost || 0) - newPaidAmount;
        
        await runQuery(`
          UPDATE projects 
          SET paidAmount = ?, remainingAmount = ?, updatedAt = ?
          WHERE id = ?
        `, [newPaidAmount, Math.max(0, remainingAmount), new Date().toISOString(), validId]);
        
        // تحديث الحالة إذا تم الدفع بالكامل
        if (remainingAmount <= 0) {
          await runQuery(`
            UPDATE projects 
            SET status = 'مكتمل', updatedAt = ?
            WHERE id = ?
          `, [new Date().toISOString(), validId]);
        }
      } catch (error) {
        handleError(error, 'تحديث حالة الدفع', ErrorType.DATABASE);
        throw error;
      }
    }, 'Update Payment', 'database', { id, paidAmount });
  }
}
