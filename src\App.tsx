
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";

import { AppProvider } from "./contexts/AppContext";
import Index from "./pages/Index";
import Materials from "./pages/Materials";
import Reports from "./pages/Reports";
import Salaries from "./pages/Salaries";
import Treasury from "./pages/Treasury";
import Customers from "./pages/Customers";
import Invoices from "./pages/Invoices";
import NotFound from "./pages/NotFound";
import MaterialStockMonitor from "./components/MaterialStockMonitor";
import ErrorBoundary from "./components/ErrorBoundary";
import { validateConfig } from "./config/appConfig";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 دقائق
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => {
  useEffect(() => {
    // تهيئة التطبيق
    const initializeApp = async () => {
      try {
        // التحقق من صحة الإعدادات
        const configValidation = validateConfig();
        if (!configValidation.isValid) {
          console.warn('تحذيرات في إعدادات التطبيق:', configValidation.errors);
        }

        // تهيئة التطبيق الأساسية

        console.log('تم تهيئة التطبيق بنجاح');
      } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
      }
    };

    initializeApp();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <AppProvider>
            <MaterialStockMonitor />
            <Toaster />
            <Sonner />
            <HashRouter>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/materials" element={<Materials />} />
                <Route path="/treasury" element={<Treasury />} />
                <Route path="/reports" element={<Reports />} />
                <Route path="/salaries" element={<Salaries />} />
                <Route path="/customers" element={<Customers />} />
                <Route path="/invoices" element={<Invoices />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </HashRouter>
          </AppProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
