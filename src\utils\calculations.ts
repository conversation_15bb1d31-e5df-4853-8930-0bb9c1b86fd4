
// Calculate total cost based on area, material, worker, factory, and designer
export const calculateTotalCost = (
  area: number, 
  materialPrice: number,
  workerPrice: number,
  factoryPrice: number,
  designerPrice: number
): number => {
  const baseCost = area * materialPrice;
  const workerCost = area * workerPrice;
  const factoryCost = area * factoryPrice;
  const designerCost = area * designerPrice;
  
  return baseCost + workerCost + factoryCost + designerCost;
};

// Format numbers with proper decimal places and thousands separators
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('ar-LY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num);
};

// Format currency in Libyan Dinar
export const formatLibyanDinar = (amount: number): string => {
  const formattedAmount = formatNumber(amount);
  return `${formattedAmount} د.ل`;
};

// Calculate breakdown costs
export const calculateBreakdownCosts = (
  area: number,
  materialPrice: number,
  workerPrice: number,
  factoryPrice: number,
  designerPrice: number
) => {
  return {
    materialCost: area * materialPrice,
    workerCost: area * workerPrice,
    factoryCost: area * factoryPrice,
    designerCost: area * designerPrice
  };
};

// Calculate tax (if needed in future)
export const calculateTax = (amount: number, taxRate: number = 0): number => {
  return amount * (taxRate / 100);
};

// Calculate discount (if needed in future)
export const calculateDiscount = (amount: number, discountRate: number): number => {
  return amount * (discountRate / 100);
};

export const calculateLaborCost = (hours: number, rate: number) => {
  const baseCost = hours * rate;
  const overtime = Math.max(0, hours - 176) * rate * 1.5;
  return baseCost + overtime;
};
