// خدمة التحقق من صحة البيانات
// تهدف لتوحيد عمليات التحقق من البيانات في جميع أنحاء التطبيق

/**
 * التحقق من أن القيمة مصفوفة صحيحة
 */
export const ensureArray = <T>(value: any): T[] => {
  return Array.isArray(value) ? value : [];
};

/**
 * التحقق من أن القيمة رقم صحيح
 */
export const ensureNumber = (value: any, defaultValue: number = 0): number => {
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
};

/**
 * التحقق من أن القيمة نص صحيح
 */
export const ensureString = (value: any, defaultValue: string = ''): string => {
  return typeof value === 'string' ? value : defaultValue;
};

/**
 * التحقق من أن القيمة كائن صحيح
 */
export const ensureObject = <T>(value: any, defaultValue: T): T => {
  return value && typeof value === 'object' && !Array.isArray(value) ? value : defaultValue;
};

/**
 * التحقق من صحة البيانات المالية
 */
export const validateFinancialData = (data: any) => {
  return {
    totalIncome: ensureNumber(data?.totalIncome),
    totalExpenses: ensureNumber(data?.totalExpenses),
    currentBalance: ensureNumber(data?.currentBalance),
    monthlyIncome: ensureNumber(data?.monthlyIncome),
    monthlyExpenses: ensureNumber(data?.monthlyExpenses),
    projectPayments: ensureNumber(data?.projectPayments),
    salaryPayments: ensureNumber(data?.salaryPayments),
    generalExpenses: ensureNumber(data?.generalExpenses)
  };
};

/**
 * التحقق من صحة بيانات المشروع
 */
export const validateProjectData = (project: any) => {
  return {
    ...project,
    totalCost: ensureNumber(project?.totalCost),
    materialCost: ensureNumber(project?.materialCost),
    workerCost: ensureNumber(project?.workerCost),
    factoryCost: ensureNumber(project?.factoryCost),
    designerCost: ensureNumber(project?.designerCost),
    paidAmount: ensureNumber(project?.paidAmount),
    remainingAmount: ensureNumber(project?.remainingAmount),
    area: ensureNumber(project?.area)
  };
};

/**
 * التحقق من صحة بيانات المواد
 */
export const validateMaterialData = (material: any) => {
  return {
    ...material,
    pricePerSqm: ensureNumber(material?.pricePerSqm),
    availableQuantity: ensureNumber(material?.availableQuantity),
    minQuantity: ensureNumber(material?.minQuantity),
    purchasePrice: ensureNumber(material?.purchasePrice),
    salePrice: ensureNumber(material?.salePrice)
  };
};

/**
 * التحقق من صحة بيانات العامل
 */
export const validateWorkerData = (worker: any) => {
  return {
    ...worker,
    pricePerSqm: ensureNumber(worker?.pricePerSqm),
    totalProjects: ensureNumber(worker?.totalProjects),
    totalEarnings: ensureNumber(worker?.totalEarnings)
  };
};

/**
 * معالجة آمنة لعمليات reduce
 */
export const safeReduce = <T, R>(
  array: any,
  callback: (accumulator: R, currentValue: T, currentIndex: number, array: T[]) => R,
  initialValue: R
): R => {
  const safeArray = ensureArray<T>(array);
  return safeArray.reduce(callback, initialValue);
};

/**
 * معالجة آمنة لعمليات map
 */
export const safeMap = <T, R>(
  array: any,
  callback: (value: T, index: number, array: T[]) => R
): R[] => {
  const safeArray = ensureArray<T>(array);
  return safeArray.map(callback);
};

/**
 * معالجة آمنة لعمليات filter
 */
export const safeFilter = <T>(
  array: any,
  callback: (value: T, index: number, array: T[]) => boolean
): T[] => {
  const safeArray = ensureArray<T>(array);
  return safeArray.filter(callback);
};

/**
 * التحقق من صحة البيانات قبل العمليات الحسابية
 */
export const validateCalculationInputs = (inputs: any) => {
  const errors: string[] = [];
  
  if (!inputs.area || inputs.area <= 0) {
    errors.push('المساحة مطلوبة ويجب أن تكون أكبر من صفر');
  }
  
  if (!inputs.selectedMaterial) {
    errors.push('يجب اختيار مادة');
  }
  
  if (!inputs.selectedWorker) {
    errors.push('يجب اختيار عامل');
  }
  
  if (!inputs.selectedFactory) {
    errors.push('يجب اختيار مصنع');
  }
  
  if (!inputs.selectedDesigner) {
    errors.push('يجب اختيار مصمم');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * تنظيف البيانات من القيم الفارغة أو غير الصحيحة
 */
export const cleanData = (data: any): any => {
  if (Array.isArray(data)) {
    return data.filter(item => item != null).map(cleanData);
  }
  
  if (data && typeof data === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (value != null) {
        cleaned[key] = cleanData(value);
      }
    }
    return cleaned;
  }
  
  return data;
};

/**
 * التحقق من صحة معرف الكائن
 */
export const validateId = (id: any): string | null => {
  if (typeof id === 'string' && id.trim().length > 0) {
    return id.trim();
  }
  return null;
};

/**
 * التحقق من صحة رقم الهاتف
 */
export const validatePhone = (phone: any): string | null => {
  if (typeof phone === 'string') {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length >= 9) {
      return cleaned;
    }
  }
  return null;
};

/**
 * التحقق من صحة البريد الإلكتروني
 */
export const validateEmail = (email: any): string | null => {
  if (typeof email === 'string') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(email)) {
      return email.toLowerCase();
    }
  }
  return null;
};

/**
 * التحقق من صحة التاريخ
 */
export const validateDate = (date: any): string | null => {
  if (typeof date === 'string') {
    const parsedDate = new Date(date);
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate.toISOString();
    }
  }
  if (date instanceof Date && !isNaN(date.getTime())) {
    return date.toISOString();
  }
  return null;
};

/**
 * معالجة الأخطاء بشكل موحد
 */
export const handleError = (error: any, context: string = 'عملية غير محددة'): void => {
  console.error(`خطأ في ${context}:`, error);
  
  // يمكن إضافة المزيد من معالجة الأخطاء هنا
  // مثل إرسال التقارير أو عرض رسائل للمستخدم
};

/**
 * تحويل البيانات إلى تنسيق آمن للعرض
 */
export const safeDisplayData = (data: any): string => {
  if (data == null) return '-';
  if (typeof data === 'string') return data;
  if (typeof data === 'number') return data.toString();
  if (typeof data === 'boolean') return data ? 'نعم' : 'لا';
  return JSON.stringify(data);
};
