// أدوات مساعدة موحدة للتطبيق
import { toast } from "@/hooks/use-toast";

// أنواع الأخطاء
export enum ErrorType {
  VALIDATION = 'validation',
  NETWORK = 'network',
  DATABASE = 'database',
  PERMISSION = 'permission',
  UNKNOWN = 'unknown'
}

// واجهة الخطأ الموحدة
export interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  code?: string;
}

// معالج الأخطاء الموحد
export const handleError = (error: any, context?: string): AppError => {
  let appError: AppError;

  if (error instanceof Error) {
    appError = {
      type: ErrorType.UNKNOWN,
      message: error.message,
      details: error.stack
    };
  } else if (typeof error === 'string') {
    appError = {
      type: ErrorType.UNKNOWN,
      message: error
    };
  } else if (error && typeof error === 'object') {
    appError = {
      type: error.type || ErrorType.UNKNOWN,
      message: error.message || 'حدث خطأ غير معروف',
      details: error.details,
      code: error.code
    };
  } else {
    appError = {
      type: ErrorType.UNKNOWN,
      message: 'حدث خطأ غير معروف'
    };
  }

  // إضافة السياق إذا كان متوفراً
  if (context) {
    appError.message = `${context}: ${appError.message}`;
  }

  // عرض رسالة الخطأ للمستخدم
  toast({
    title: "خطأ",
    description: appError.message,
    variant: "destructive",
  });

  // تسجيل الخطأ في الكونسول للتطوير
  console.error('App Error:', appError);

  return appError;
};

// معالج النجاح الموحد
export const handleSuccess = (message: string, description?: string) => {
  toast({
    title: "نجح",
    description: message,
    variant: "default",
  });
};

// التحقق من صحة البيانات
export const validateRequired = (value: any, fieldName: string): boolean => {
  if (value === null || value === undefined || value === '') {
    throw new Error(`${fieldName} مطلوب`);
  }
  return true;
};

export const validateNumber = (value: any, fieldName: string, min?: number, max?: number): boolean => {
  validateRequired(value, fieldName);
  
  const num = Number(value);
  if (isNaN(num)) {
    throw new Error(`${fieldName} يجب أن يكون رقماً صحيحاً`);
  }
  
  if (min !== undefined && num < min) {
    throw new Error(`${fieldName} يجب أن يكون أكبر من أو يساوي ${min}`);
  }
  
  if (max !== undefined && num > max) {
    throw new Error(`${fieldName} يجب أن يكون أصغر من أو يساوي ${max}`);
  }
  
  return true;
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new Error('البريد الإلكتروني غير صحيح');
  }
  return true;
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[0-9+\-\s()]+$/;
  if (!phoneRegex.test(phone)) {
    throw new Error('رقم الهاتف غير صحيح');
  }
  return true;
};

// دوال مساعدة للبيانات
export const ensureArray = <T>(value: T | T[] | null | undefined): T[] => {
  if (Array.isArray(value)) return value;
  if (value === null || value === undefined) return [];
  return [value];
};

export const safeParseNumber = (value: any, defaultValue: number = 0): number => {
  if (typeof value === 'number' && !isNaN(value)) return value;
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  return defaultValue;
};

export const safeParseInt = (value: any, defaultValue: number = 0): number => {
  if (typeof value === 'number' && !isNaN(value)) return Math.floor(value);
  if (typeof value === 'string') {
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  return defaultValue;
};

export const formatCurrency = (amount: number, currency: string = 'د.ل'): string => {
  return `${amount.toLocaleString('ar-LY', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })} ${currency}`;
};

export const formatDate = (date: Date | string | number): string => {
  const d = new Date(date);
  if (isNaN(d.getTime())) return 'تاريخ غير صحيح';
  
  return d.toLocaleDateString('ar-LY', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const formatDateTime = (date: Date | string | number): string => {
  const d = new Date(date);
  if (isNaN(d.getTime())) return 'تاريخ غير صحيح';
  
  return d.toLocaleString('ar-LY', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// دوال مساعدة للنصوص
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const capitalizeFirst = (text: string): string => {
  if (!text) return text;
  return text.charAt(0).toUpperCase() + text.slice(1);
};

// دوال مساعدة للكائنات
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as any;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as any;
  
  const cloned = {} as T;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  return cloned;
};

export const isEmpty = (obj: any): boolean => {
  if (obj === null || obj === undefined) return true;
  if (typeof obj === 'string') return obj.trim() === '';
  if (Array.isArray(obj)) return obj.length === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
};

// دوال مساعدة للتحميل والحفظ
export const downloadJSON = (data: any, filename: string) => {
  const blob = new Blob([JSON.stringify(data, null, 2)], {
    type: 'application/json'
  });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const downloadCSV = (data: any[], filename: string) => {
  if (data.length === 0) return;
  
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// دوال مساعدة للتخزين المحلي
export const setLocalStorage = (key: string, value: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

export const getLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return defaultValue;
  }
};

export const removeLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing from localStorage:', error);
  }
};

// دوال مساعدة للتأخير والانتظار
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
