import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 بدء عملية بناء تطبيق سطح المكتب...\n');

// Check if required files exist
const requiredFiles = [
  'public/electron.js',
  'public/preload.js',
  'package.json'
];

console.log('✅ فحص الملفات المطلوبة...');
for (const file of requiredFiles) {
  if (!fs.existsSync(file)) {
    console.error(`❌ الملف المطلوب غير موجود: ${file}`);
    process.exit(1);
  }
}

try {
  // Install dependencies if node_modules doesn't exist
  if (!fs.existsSync('node_modules')) {
    console.log('📦 تثبيت التبعيات...');
    execSync('npm install', { stdio: 'inherit' });
  }

  // Build the React app
  console.log('🔨 بناء تطبيق React...');
  execSync('npm run build', { stdio: 'inherit' });

  // Check if dist folder was created
  if (!fs.existsSync('dist')) {
    console.error('❌ فشل في إنشاء مجلد dist');
    process.exit(1);
  }

  // Build Electron app
  console.log('⚡ بناء تطبيق Electron...');
  execSync('npm run electron:dist', { stdio: 'inherit' });

  console.log('\n🎉 تم بناء التطبيق بنجاح!');
  console.log('📁 ستجد ملفات التثبيت في مجلد: dist-electron');
  
  // List generated files
  const distElectronPath = 'dist-electron';
  if (fs.existsSync(distElectronPath)) {
    console.log('\n📋 الملفات المُنشأة:');
    const files = fs.readdirSync(distElectronPath);
    files.forEach(file => {
      const filePath = path.join(distElectronPath, file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`   • ${file} (${size} MB)`);
    });
  }

} catch (error) {
  console.error('❌ حدث خطأ أثناء البناء:', error.message);
  process.exit(1);
}
