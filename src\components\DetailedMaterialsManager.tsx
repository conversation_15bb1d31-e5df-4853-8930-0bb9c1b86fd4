import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Package,
  AlertTriangle,
  Save,
  X
} from 'lucide-react';
import { DetailedMaterial, MaterialCategory, DEFAULT_MATERIAL_CATEGORIES, DEFAULT_MATERIALS } from '@/types/materials';
import { formatLibyanDinar } from '@/utils/calculations';
import { 
  getDetailedMaterials, 
  getMaterialCategories,
  addDetailedMaterial,
  updateDetailedMaterial,
  deleteDetailedMaterial,
  addMaterialCategory,
  initializeDefaultData
} from '@/services/materialsService';
import { useToast } from '@/hooks/use-toast';

const DetailedMaterialsManager: React.FC = () => {
  const { toast } = useToast();
  const [materials, setMaterials] = useState<DetailedMaterial[]>([]);
  const [categories, setCategories] = useState<MaterialCategory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [isAddingMaterial, setIsAddingMaterial] = useState(false);
  const [editingMaterial, setEditingMaterial] = useState<DetailedMaterial | null>(null);
  const [loading, setLoading] = useState(true);

  const [materialForm, setMaterialForm] = useState({
    code: '',
    name: '',
    description: '',
    categoryId: '',
    unit: '',
    purchasePrice: '',
    salePrice: '',
    availableQuantity: '',
    minQuantity: '',
    supplier: '',
    notes: '',
    isActive: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [materialsData, categoriesData] = await Promise.all([
        getDetailedMaterials(),
        getMaterialCategories()
      ]);
      
      setMaterials(materialsData);
      setCategories(categoriesData);

      // إذا لم توجد فئات، قم بتهيئة البيانات الافتراضية
      if (categoriesData.length === 0) {
        await initializeDefaultData();
        // إعادة تحميل البيانات بعد التهيئة
        const [newMaterialsData, newCategoriesData] = await Promise.all([
          getDetailedMaterials(),
          getMaterialCategories()
        ]);
        setMaterials(newMaterialsData);
        setCategories(newCategoriesData);
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات المواد",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setMaterialForm({
      code: '',
      name: '',
      description: '',
      categoryId: '',
      unit: '',
      purchasePrice: '',
      salePrice: '',
      availableQuantity: '',
      minQuantity: '',
      supplier: '',
      notes: '',
      isActive: true
    });
    setEditingMaterial(null);
    setIsAddingMaterial(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!materialForm.code || !materialForm.name || !materialForm.categoryId || 
        !materialForm.unit || !materialForm.purchasePrice || !materialForm.salePrice) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    const category = categories.find(c => c.id === materialForm.categoryId);
    if (!category) {
      toast({
        title: "خطأ في الفئة",
        description: "يرجى اختيار فئة صحيحة",
        variant: "destructive"
      });
      return;
    }

    const materialData = {
      code: materialForm.code,
      name: materialForm.name,
      description: materialForm.description,
      category,
      unit: materialForm.unit,
      purchasePrice: parseFloat(materialForm.purchasePrice),
      salePrice: parseFloat(materialForm.salePrice),
      availableQuantity: parseFloat(materialForm.availableQuantity) || 0,
      minQuantity: parseFloat(materialForm.minQuantity) || 0,
      supplier: materialForm.supplier,
      notes: materialForm.notes,
      isActive: materialForm.isActive
    };

    try {
      if (editingMaterial) {
        // تحديث مادة موجودة
        const success = await updateDetailedMaterial(editingMaterial.id, materialData);
        if (success) {
          toast({
            title: "تم التحديث",
            description: "تم تحديث المادة بنجاح"
          });
          await loadData();
          resetForm();
        } else {
          throw new Error('فشل في التحديث');
        }
      } else {
        // إضافة مادة جديدة
        const id = await addDetailedMaterial(materialData);
        if (id) {
          toast({
            title: "تم الإضافة",
            description: "تم إضافة المادة بنجاح"
          });
          await loadData();
          resetForm();
        } else {
          throw new Error('فشل في الإضافة');
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ المادة:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ المادة",
        variant: "destructive"
      });
    }
  };

  const handleEdit = (material: DetailedMaterial) => {
    setMaterialForm({
      code: material.code,
      name: material.name,
      description: material.description || '',
      categoryId: material.category.id,
      unit: material.unit,
      purchasePrice: material.purchasePrice.toString(),
      salePrice: material.salePrice.toString(),
      availableQuantity: material.availableQuantity.toString(),
      minQuantity: material.minQuantity.toString(),
      supplier: material.supplier || '',
      notes: material.notes || '',
      isActive: material.isActive
    });
    setEditingMaterial(material);
    setIsAddingMaterial(true);
  };

  const handleDelete = async (material: DetailedMaterial) => {
    if (window.confirm(`هل أنت متأكد من حذف المادة "${material.name}"؟`)) {
      try {
        const success = await deleteDetailedMaterial(material.id);
        if (success) {
          toast({
            title: "تم الحذف",
            description: "تم حذف المادة بنجاح"
          });
          await loadData();
        } else {
          throw new Error('فشل في الحذف');
        }
      } catch (error) {
        console.error('خطأ في حذف المادة:', error);
        toast({
          title: "خطأ في الحذف",
          description: "حدث خطأ أثناء حذف المادة",
          variant: "destructive"
        });
      }
    }
  };

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = !searchQuery || 
      material.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || material.category.id === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  if (loading) {
    return (
      <Card className="shadow-lg">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>جاري تحميل المواد...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* شريط البحث والفلترة */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            إدارة المواد التفصيلية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في المواد..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة حسب الفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفئات</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button 
              onClick={() => setIsAddingMaterial(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              إضافة مادة
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* نموذج إضافة/تعديل المادة */}
      {isAddingMaterial && (
        <Card className="shadow-lg border-blue-200">
          <CardHeader className="bg-blue-50">
            <CardTitle className="flex items-center justify-between">
              <span>{editingMaterial ? 'تعديل المادة' : 'إضافة مادة جديدة'}</span>
              <Button variant="ghost" size="sm" onClick={resetForm}>
                <X className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="code">رقم المادة *</Label>
                  <Input
                    id="code"
                    placeholder="مثل: H-1, MDF-18"
                    value={materialForm.code}
                    onChange={(e) => setMaterialForm(prev => ({ ...prev, code: e.target.value }))}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="name">اسم المادة *</Label>
                  <Input
                    id="name"
                    placeholder="اسم المادة"
                    value={materialForm.name}
                    onChange={(e) => setMaterialForm(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  placeholder="وصف المادة"
                  value={materialForm.description}
                  onChange={(e) => setMaterialForm(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">الفئة *</Label>
                  <Select 
                    value={materialForm.categoryId} 
                    onValueChange={(value) => setMaterialForm(prev => ({ ...prev, categoryId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="unit">الوحدة *</Label>
                  <Input
                    id="unit"
                    placeholder="قطعة، متر، كيلو، لتر..."
                    value={materialForm.unit}
                    onChange={(e) => setMaterialForm(prev => ({ ...prev, unit: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="purchasePrice">سعر الشراء (د.ل) *</Label>
                  <Input
                    id="purchasePrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={materialForm.purchasePrice}
                    onChange={(e) => setMaterialForm(prev => ({ ...prev, purchasePrice: e.target.value }))}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="salePrice">سعر البيع (د.ل) *</Label>
                  <Input
                    id="salePrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={materialForm.salePrice}
                    onChange={(e) => setMaterialForm(prev => ({ ...prev, salePrice: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="availableQuantity">الكمية المتوفرة</Label>
                  <Input
                    id="availableQuantity"
                    type="number"
                    step="0.01"
                    placeholder="0"
                    value={materialForm.availableQuantity}
                    onChange={(e) => setMaterialForm(prev => ({ ...prev, availableQuantity: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="minQuantity">الحد الأدنى للكمية</Label>
                  <Input
                    id="minQuantity"
                    type="number"
                    step="0.01"
                    placeholder="0"
                    value={materialForm.minQuantity}
                    onChange={(e) => setMaterialForm(prev => ({ ...prev, minQuantity: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplier">المورد</Label>
                <Input
                  id="supplier"
                  placeholder="اسم المورد"
                  value={materialForm.supplier}
                  onChange={(e) => setMaterialForm(prev => ({ ...prev, supplier: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">ملاحظات</Label>
                <Textarea
                  id="notes"
                  placeholder="ملاحظات إضافية"
                  value={materialForm.notes}
                  onChange={(e) => setMaterialForm(prev => ({ ...prev, notes: e.target.value }))}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={materialForm.isActive}
                  onCheckedChange={(checked) => setMaterialForm(prev => ({ ...prev, isActive: checked }))}
                />
                <Label htmlFor="isActive">مادة نشطة</Label>
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="bg-green-600 hover:bg-green-700">
                  <Save className="h-4 w-4 mr-2" />
                  {editingMaterial ? 'تحديث' : 'حفظ'}
                </Button>
                <Button type="button" variant="outline" onClick={resetForm}>
                  إلغاء
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* جدول المواد */}
      <Card className="shadow-lg">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">رقم المادة</TableHead>
                  <TableHead className="text-right">اسم المادة</TableHead>
                  <TableHead className="text-right">الفئة</TableHead>
                  <TableHead className="text-right">الوحدة</TableHead>
                  <TableHead className="text-right">سعر الشراء</TableHead>
                  <TableHead className="text-right">سعر البيع</TableHead>
                  <TableHead className="text-right">المخزن</TableHead>
                  <TableHead className="text-right">المورد</TableHead>
                  <TableHead className="text-right">الحالة</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMaterials.map((material) => {
                  const isLowStock = material.availableQuantity <= material.minQuantity;
                  const profit = material.salePrice - material.purchasePrice;
                  const profitMargin = material.purchasePrice > 0 ? (profit / material.purchasePrice) * 100 : 0;

                  return (
                    <TableRow 
                      key={material.id}
                      className={isLowStock ? 'bg-red-50' : ''}
                    >
                      <TableCell className="font-mono font-semibold">
                        {material.code}
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <div className="font-medium">{material.name}</div>
                          {material.description && (
                            <div className="text-sm text-gray-500">{material.description}</div>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge 
                          variant="outline"
                          style={{
                            backgroundColor: material.category.color + '20',
                            borderColor: material.category.color,
                            color: material.category.color
                          }}
                        >
                          {material.category.name}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>{material.unit}</TableCell>
                      
                      <TableCell className="font-semibold text-red-600">
                        {formatLibyanDinar(material.purchasePrice)}
                      </TableCell>
                      
                      <TableCell className="font-semibold text-green-600">
                        {formatLibyanDinar(material.salePrice)}
                        <div className="text-xs text-gray-500">
                          +{profitMargin.toFixed(1)}%
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <span className={isLowStock ? 'text-red-600 font-semibold' : ''}>
                            {material.availableQuantity}
                          </span>
                          {isLowStock && <AlertTriangle className="h-4 w-4 text-red-500" />}
                        </div>
                      </TableCell>
                      
                      <TableCell className="text-sm">
                        {material.supplier || '-'}
                      </TableCell>
                      
                      <TableCell>
                        <Badge variant={material.isActive ? 'default' : 'secondary'}>
                          {material.isActive ? 'نشط' : 'غير نشط'}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(material)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDelete(material)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredMaterials.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد مواد تطابق معايير البحث</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DetailedMaterialsManager;
