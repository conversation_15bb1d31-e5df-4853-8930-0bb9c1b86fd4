import { useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { getDetailedMaterials } from '@/services/materialsService';

function MaterialStockMonitor() {
  const { toast } = useToast();
  
  useEffect(() => {
    const checkMaterialStock = async () => {
      try {
        const materials = await getDetailedMaterials();
        materials.forEach(material => {
          if (material.availableQuantity < material.minQuantity) {
            toast({
              title: "نقص في المخزون",
              description: `نقص في المخزون: ${material.name}`,
              variant: "destructive"
            });
          }
        });
      } catch (error) {
        console.error('خطأ في فحص المخزون:', error);
      }
    };
    
    checkMaterialStock();
  }, []);
  
  return null; // هذا مكون وظيفي فقط، لا يعرض أي واجهة
}

export default MaterialStockMonitor;