import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calculator, 
  Package, 
  TrendingUp, 
  DollarSign,
  ShoppingCart,
  Warehouse
} from 'lucide-react';
import { 
  DetailedMaterial, 
  MaterialCategory, 
  MaterialCalculation,
  ProjectMaterialsSummary 
} from '@/types/materials';
import { formatLibyanDinar } from '@/utils/calculations';
import { 
  getDetailedMaterials, 
  getMaterialCategories,
  calculateMaterialCosts 
} from '@/services/materialsService';
import MaterialsTable from './MaterialsTable';
import { useToast } from '@/hooks/use-toast';

interface MaterialSelectorProps {
  onMaterialsChange: (summary: ProjectMaterialsSummary) => void;
  initialMaterials?: MaterialCalculation[];
}

const MaterialSelector: React.FC<MaterialSelectorProps> = ({
  onMaterialsChange,
  initialMaterials = []
}) => {
  const { toast } = useToast();
  const [materials, setMaterials] = useState<DetailedMaterial[]>([]);
  const [categories, setCategories] = useState<MaterialCategory[]>([]);
  const [selectedMaterials, setSelectedMaterials] = useState<MaterialCalculation[]>(initialMaterials);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // حساب التكاليف عند تغيير المواد المختارة
    const summary = calculateMaterialCosts(selectedMaterials);
    onMaterialsChange(summary);
  }, [selectedMaterials, onMaterialsChange]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [materialsData, categoriesData] = await Promise.all([
        getDetailedMaterials(),
        getMaterialCategories()
      ]);
      
      setMaterials(materialsData);
      setCategories(categoriesData);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات المواد",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMaterialSelect = (material: DetailedMaterial, quantity: number) => {
    const totalPurchase = quantity * material.purchasePrice;
    const totalSale = quantity * material.salePrice;
    const profit = totalSale - totalPurchase;

    const materialCalculation: MaterialCalculation = {
      materialId: material.id,
      materialName: material.name,
      materialCode: material.code,
      requiredQuantity: quantity,
      purchasePrice: material.purchasePrice,
      salePrice: material.salePrice,
      totalPurchase,
      totalSale,
      profit,
      source: material.availableQuantity >= quantity ? 'stock' : 'external'
    };

    setSelectedMaterials(prev => {
      const existing = prev.find(m => m.materialId === material.id);
      if (existing) {
        return prev.map(m => 
          m.materialId === material.id ? materialCalculation : m
        );
      } else {
        return [...prev, materialCalculation];
      }
    });
  };

  const handleMaterialRemove = (materialId: string) => {
    setSelectedMaterials(prev => prev.filter(m => m.materialId !== materialId));
  };

  const handleQuantityChange = (materialId: string, quantity: number) => {
    const material = materials.find(m => m.id === materialId);
    if (material) {
      if (quantity > 0) {
        handleMaterialSelect(material, quantity);
      } else {
        handleMaterialRemove(materialId);
      }
    }
  };

  const clearAllMaterials = () => {
    setSelectedMaterials([]);
    toast({
      title: "تم مسح المواد",
      description: "تم مسح جميع المواد المختارة"
    });
  };

  const summary = calculateMaterialCosts(selectedMaterials);

  if (loading) {
    return (
      <Card className="shadow-lg">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>جاري تحميل المواد...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* ملخص التكاليف */}
      <Card className="shadow-lg bg-gradient-to-r from-blue-50 to-green-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            ملخص تكاليف المواد
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-white rounded-lg shadow">
              <ShoppingCart className="h-8 w-8 mx-auto mb-2 text-red-500" />
              <div className="text-sm text-gray-600">إجمالي الشراء</div>
              <div className="text-lg font-bold text-red-600">
                {formatLibyanDinar(summary.totalPurchaseCost)}
              </div>
            </div>
            
            <div className="text-center p-4 bg-white rounded-lg shadow">
              <DollarSign className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <div className="text-sm text-gray-600">إجمالي البيع</div>
              <div className="text-lg font-bold text-green-600">
                {formatLibyanDinar(summary.totalSaleCost)}
              </div>
            </div>
            
            <div className="text-center p-4 bg-white rounded-lg shadow">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <div className="text-sm text-gray-600">المكسب</div>
              <div className="text-lg font-bold text-blue-600">
                {formatLibyanDinar(summary.totalProfit)}
              </div>
            </div>
            
            <div className="text-center p-4 bg-white rounded-lg shadow">
              <Warehouse className="h-8 w-8 mx-auto mb-2 text-purple-500" />
              <div className="text-sm text-gray-600">من المخزن</div>
              <div className="text-lg font-bold text-purple-600">
                {formatLibyanDinar(summary.stockCost)}
              </div>
            </div>
          </div>
          
          {selectedMaterials.length > 0 && (
            <div className="mt-4 flex justify-between items-center">
              <Badge variant="secondary">
                {selectedMaterials.length} مادة مختارة
              </Badge>
              <Button variant="outline" onClick={clearAllMaterials}>
                مسح جميع المواد
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* جدول المواد */}
      <Tabs defaultValue="all" className="w-full">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
          <TabsList className="grid w-full md:w-auto grid-cols-2 md:grid-cols-6">
            <TabsTrigger value="all">الكل</TabsTrigger>
            {categories.map(category => (
              <TabsTrigger key={category.id} value={category.id}>
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="فلترة حسب الفئة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الفئات</SelectItem>
              {categories.map(category => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <TabsContent value="all">
          <MaterialsTable
            materials={materials}
            selectedMaterials={selectedMaterials}
            onMaterialSelect={handleMaterialSelect}
            onMaterialRemove={handleMaterialRemove}
            onQuantityChange={handleQuantityChange}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            categoryFilter={categoryFilter}
          />
        </TabsContent>

        {categories.map(category => (
          <TabsContent key={category.id} value={category.id}>
            <MaterialsTable
              materials={materials}
              selectedMaterials={selectedMaterials}
              onMaterialSelect={handleMaterialSelect}
              onMaterialRemove={handleMaterialRemove}
              onQuantityChange={handleQuantityChange}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              categoryFilter={category.id}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default MaterialSelector;
