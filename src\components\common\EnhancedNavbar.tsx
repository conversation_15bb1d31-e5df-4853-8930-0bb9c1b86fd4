// شريط التنقل المحسن
import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { 
  Menu, 
  Home, 
  Package, 
  FileText, 
  DollarSign, 
  Users, 
  UserCog, 
  Receipt,
  BarChart3,
  Settings,
  LogOut,
  Search
} from "lucide-react";

import NotificationSystem, { useNotifications } from "./NotificationSystem";
import { themeClasses } from "@/styles/theme";
import { cn } from "@/lib/utils";

interface NavItem {
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  description?: string;
}

const EnhancedNavbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const location = useLocation();
  
  // استخدام نظام الإشعارات
  const {
    notifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll
  } = useNotifications();

  const navItems: NavItem[] = [
    { 
      name: "الرئيسية", 
      path: "/", 
      icon: Home,
      description: "لوحة التحكم الرئيسية"
    },
    { 
      name: "المواد والعمال", 
      path: "/materials", 
      icon: Package,
      description: "إدارة المواد والموارد"
    },
    { 
      name: "الفواتير", 
      path: "/invoices", 
      icon: Receipt,
      description: "إدارة الفواتير والمدفوعات"
    },
    { 
      name: "التقارير", 
      path: "/reports", 
      icon: BarChart3,
      description: "التقارير والإحصائيات"
    },
    { 
      name: "الخزينة", 
      path: "/treasury", 
      icon: DollarSign,
      description: "إدارة الخزينة والمالية"
    },
    { 
      name: "الرواتب", 
      path: "/salaries", 
      icon: UserCog,
      description: "إدارة رواتب الموظفين"
    },
    { 
      name: "العملاء", 
      path: "/customers", 
      icon: Users,
      description: "إدارة بيانات العملاء"
    },
  ];

  const isActive = (path: string) => location.pathname === path;

  const NavLink: React.FC<{ 
    item: NavItem; 
    mobile?: boolean; 
    onClick?: () => void;
  }> = ({ item, mobile = false, onClick }) => {
    const Icon = item.icon;
    const active = isActive(item.path);
    
    return (
      <Link
        to={item.path}
        onClick={onClick}
        className={cn(
          'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium',
          themeClasses.transition,
          mobile ? 'w-full justify-start' : '',
          active
            ? "bg-blue-100 text-blue-700 shadow-sm"
            : "text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:shadow-sm"
        )}
      >
        <Icon className="h-4 w-4" />
        <span>{item.name}</span>
        {item.badge && (
          <Badge variant="secondary" className="ml-auto">
            {item.badge}
          </Badge>
        )}
      </Link>
    );
  };

  return (
    <nav className={cn(
      'bg-white/95 backdrop-blur-sm shadow-lg border-b border-gray-200 sticky top-0 z-50',
      themeClasses.transition
    )}>
      <div className={themeClasses.container}>
        <div className={cn(themeClasses.flexBetween, 'h-16')}>
          {/* الشعار */}
          <Link 
            to="/" 
            className={cn(
              themeClasses.flexStart,
              'space-x-2 space-x-reverse hover:opacity-80',
              themeClasses.transition
            )}
          >
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-green-600 rounded-xl flex items-center justify-center shadow-md">
              <Package className="h-6 w-6 text-white" />
            </div>
            <div className="flex flex-col">
              <span className={cn(themeClasses.heading4, 'text-gray-800 leading-tight')}>
                مصنع الأثاث
              </span>
              <span className="text-xs text-gray-500">
                نظام إدارة متكامل
              </span>
            </div>
          </Link>

          {/* روابط سطح المكتب */}
          <div className="hidden lg:flex items-center space-x-6 space-x-reverse">
            {navItems.map((item) => (
              <NavLink key={item.path} item={item} />
            ))}
          </div>

          {/* أدوات التحكم */}
          <div className="flex items-center gap-3">
            {/* البحث */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchOpen(!searchOpen)}
              className="hidden md:flex"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* الإشعارات */}
            <NotificationSystem
              notifications={notifications}
              onMarkAsRead={markAsRead}
              onMarkAllAsRead={markAllAsRead}
              onDelete={deleteNotification}
              onClearAll={clearAll}
            />

            {/* الإعدادات */}
            <Button variant="ghost" size="sm" className="hidden md:flex">
              <Settings className="h-5 w-5" />
            </Button>

            {/* قائمة الجوال */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="lg:hidden">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              
              <SheetContent side="right" className="w-80 p-0">
                <div className="flex flex-col h-full">
                  {/* رأس القائمة */}
                  <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-green-600 text-white">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                        <Package className="h-6 w-6" />
                      </div>
                      <div>
                        <h2 className="font-bold text-lg">مصنع الأثاث</h2>
                        <p className="text-sm opacity-90">نظام إدارة متكامل</p>
                      </div>
                    </div>
                  </div>

                  {/* روابط التنقل */}
                  <div className="flex-1 p-6 space-y-2">
                    <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4">
                      القوائم الرئيسية
                    </h3>
                    
                    {navItems.map((item) => (
                      <div key={item.path} className="space-y-1">
                        <NavLink 
                          item={item} 
                          mobile 
                          onClick={() => setIsOpen(false)}
                        />
                        {item.description && (
                          <p className="text-xs text-gray-500 pr-7">
                            {item.description}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* أدوات إضافية */}
                  <div className="p-6 border-t border-gray-200 space-y-2">
                    <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4">
                      أدوات
                    </h3>
                    
                    <Button 
                      variant="ghost" 
                      className="w-full justify-start gap-3"
                      onClick={() => setSearchOpen(true)}
                    >
                      <Search className="h-4 w-4" />
                      البحث
                    </Button>
                    
                    <Button 
                      variant="ghost" 
                      className="w-full justify-start gap-3"
                    >
                      <Settings className="h-4 w-4" />
                      الإعدادات
                    </Button>
                    
                    <Button 
                      variant="ghost" 
                      className="w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <LogOut className="h-4 w-4" />
                      تسجيل الخروج
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>

      {/* شريط البحث المنسدل */}
      {searchOpen && (
        <div className="border-t border-gray-200 bg-white p-4">
          <div className={themeClasses.container}>
            <div className="relative max-w-md mx-auto">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في النظام..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                autoFocus
                onBlur={() => setSearchOpen(false)}
              />
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default EnhancedNavbar;
