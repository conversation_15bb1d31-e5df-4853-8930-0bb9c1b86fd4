// واجهات البيانات لنظام إدارة مصنع الأثاث
import { ProjectMaterialsSummary } from '@/types/materials';

export interface Material {
  id: string;
  name: string;
  description: string;
  pricePerSqm: number;
  category: string;
}

export interface Worker {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  phone?: string;
}

export interface Factory {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  location?: string;
}

export interface Designer {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  phone?: string;
}

export interface Invoice {
  id: string;
  projectId: string;
  customerName: string;
  customerPhone?: string;
  totalAmount: number;
  status: 'مبدئية' | 'تصنيع' | 'مكتملة' | 'ملغية';
  type: 'مبدئية' | 'تصنيع';
  createdAt: string;
  updatedAt?: string;
  notes?: string;
}

export interface Project {
  id: string;
  customerName: string;
  customerPhone?: string;
  area: number;
  furnitureType: string;
  selectedMaterial: Material | null; // يمكن أن يكون null الآن
  selectedWorker: Worker;
  selectedFactory: Factory;
  selectedDesigner: Designer;
  totalCost: number;
  breakdown: {
    materialCost: number;
    workerCost: number;
    factoryCost: number;
    designerCost: number;
  };
  paidAmount: number;
  remainingAmount: number;
  status: 'مكتمل' | 'قيد التنفيذ' | 'متأخر' | 'ملغي';
  invoiceStatus: 'مبدئية' | 'تصنيع' | 'مكتملة';
  createdAt: string;
  completedAt?: string;
  notes?: string;
  materialsSummary?: ProjectMaterialsSummary; // إضافة ملخص المواد التفصيلية
}

export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  totalProjects: number;
  totalSpent: number;
  createdAt: string;
}

export interface Employee {
  id: string;
  name: string;
  position: string;
  baseSalary: number;
  bonuses: number;
  deductions: number;
  totalSalary: number;
  phone?: string;
  hireDate?: string;
}

export interface CashTransaction {
  id: string;
  type: 'دخل' | 'مصروف';
  category: string;
  amount: number;
  description: string;
  date: string;
  projectId?: string;
  employeeId?: string;
}

// التحقق من وجود Electron API
const isElectron = typeof window !== 'undefined' && window.electronAPI;

// وظيفة تهيئة البيانات الافتراضية
export const initializeDefaultData = async () => {
  if (isElectron) {
    try {
      // لا نحتاج لتعبئة البيانات هنا، لأنها تتم في main process (electron.js)
      // هذه الدالة موجودة فقط لاستدعائها من React لضمان بدء عملية التهيئة في Electron
      console.log('بدء تهيئة البيانات الافتراضية في Electron...');
    } catch (error) {
      console.error('خطأ في تهيئة البيانات الافتراضية:', error);
    }
  }
};

// وظائف إدارة المواد
export const getMaterials = async (): Promise<Material[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getMaterials();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب المواد:', error);
      return [];
    }
  }
  return [];
};

export const saveMaterial = async (material: Omit<Material, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addMaterial(material);
    } catch (error) {
      console.error('خطأ في حفظ المادة:', error);
      return null;
    }
  }
  return null;
};

// وظائف إدارة العمال
export const getWorkers = async (): Promise<Worker[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getWorkers();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب العمال:', error);
      return [];
    }
  }
  return [];
};

export const saveWorker = async (worker: Omit<Worker, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addWorker(worker);
    } catch (error) {
      console.error('خطأ في حفظ العامل:', error);
      return null;
    }
  }
  return null;
};

// وظائف إدارة المصانع
export const getFactories = async (): Promise<Factory[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getFactories();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب المصانع:', error);
      return [];
    }
  }
  return [];
};

export const saveFactory = async (factory: Omit<Factory, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addFactory(factory);
    } catch (error) {
      console.error('خطأ في حفظ المصنع:', error);
      return null;
    }
  }
  return null;
};

// وظائف إدارة المصممين
export const getDesigners = async (): Promise<Designer[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getDesigners();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب المصممين:', error);
      return [];
    }
  }
  return [];
};

export const saveDesigner = async (designer: Omit<Designer, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addDesigner(designer);
    } catch (error) {
      console.error('خطأ في حفظ المصمم:', error);
      return null;
    }
  }
  return null;
};

// وظائف إدارة المشاريع
export const getProjects = async (): Promise<Project[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getProjects();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب المشاريع:', error);
      return [];
    }
  }
  return [];
};

export const updateProject = async (id: string, updates: Partial<Project>): Promise<boolean> => {
  if (isElectron) {
    try {
      return await window.electronAPI.updateProject(id, updates);
    } catch (error) {
      console.error('خطأ في تحديث المشروع:', error);
      return false;
    }
  }
  return false;
};

// وظائف إدارة العملاء
export const getCustomers = async (): Promise<Customer[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getCustomers();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب العملاء:', error);
      return [];
    }
  }
  return [];
};

export const saveCustomer = async (customer: Omit<Customer, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addCustomer(customer);
    } catch (error) {
      console.error('خطأ في حفظ العميل:', error);
      return null;
    }
  }
  return null;
};

// وظائف إدارة الموظفين
export const getEmployees = async (): Promise<Employee[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getEmployees();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
      return [];
    }
  }
  return [];
};

export const saveEmployee = async (employee: Omit<Employee, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addEmployee(employee);
    } catch (error) {
      console.error('خطأ في حفظ الموظف:', error);
      return null;
    }
  }
  return null;
};

// وظائف إدارة المعاملات المالية
export const getCashTransactions = async (): Promise<CashTransaction[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.getCashTransactions();
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب المعاملات المالية:', error);
      return [];
    }
  }
  return [];
};

export const saveCashTransaction = async (transaction: Omit<CashTransaction, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addCashTransaction(transaction);
    } catch (error) {
      console.error('خطأ في حفظ المعاملة المالية:', error);
      return null;
    }
  }
  return null;
};

// وظائف مؤقتة للفواتير (سيتم تنفيذها لاحقاً)
export const getInvoices = (): Invoice[] => [];

// وظائف مؤقتة للتوافق مع الكود القديم
export const saveMaterials = (materials: Material[]) => {
  console.log('saveMaterials deprecated - استخدم saveMaterial بدلاً من ذلك');
};

export const saveWorkers = (workers: Worker[]) => {
  console.log('saveWorkers deprecated - استخدم saveWorker بدلاً من ذلك');
};

export const saveFactories = (factories: Factory[]) => {
  console.log('saveFactories deprecated - استخدم saveFactory بدلاً من ذلك');
};

export const saveDesigners = (designers: Designer[]) => {
  console.log('saveDesigners deprecated - استخدم saveDesigner بدلاً من ذلك');
};

export const saveProjects = (projects: Project[]) => {
  console.log('saveProjects deprecated');
};

export const saveInvoices = (invoices: Invoice[]) => {
  console.log('saveInvoices deprecated');
};

export const saveCustomers = (customers: Customer[]) => {
  console.log('saveCustomers deprecated');
};

export const saveEmployees = (employees: Employee[]) => {
  console.log('saveEmployees deprecated');
};

export const saveCashTransactions = (transactions: CashTransaction[]) => {
  console.log('saveCashTransactions deprecated');
};

export const addProject = async (project: Omit<Project, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addProject(project);
    } catch (error) {
      console.error('خطأ في حفظ المشروع:', error);
      return null;
    }
  }
  return null;
};

export const updateCustomerData = (name: string, phone?: string, projectCost?: number) => {
  console.log('updateCustomerData - سيتم تنفيذه لاحقاً');
};

export const convertInvoiceToManufacturing = (invoiceId: string) => {
  console.log('convertInvoiceToManufacturing - سيتم تنفيذه لاحقاً');
};

export const addCashTransaction = async (transaction: Omit<CashTransaction, 'id'>): Promise<string | null> => {
  return await saveCashTransaction(transaction);
};

export const getCashSummary = async () => {
  if (isElectron) {
    try {
      return await window.electronAPI.getCashSummary();
    } catch (error) {
      console.error('خطأ في جلب ملخص الخزينة:', error);
      return {
        totalIncome: 0,
        totalExpenses: 0,
        currentBalance: 0,
        monthlyIncome: 0,
        monthlyExpenses: 0,
        projectPayments: 0,
        salaryPayments: 0,
        generalExpenses: 0
      };
    }
  }
  return {
    totalIncome: 0,
    totalExpenses: 0,
    currentBalance: 0,
    monthlyIncome: 0,
    monthlyExpenses: 0,
    projectPayments: 0,
    salaryPayments: 0,
    generalExpenses: 0
  };
};

// وظائف إضافية مطلوبة
export const getCustomerInvoices = (customerId: string): Invoice[] => {
  console.log('getCustomerInvoices - سيتم تنفيذه لاحقاً');
  return [];
};

export const getCustomerProjects = (customerId: string): Project[] => {
  console.log('getCustomerProjects - سيتم تنفيذه لاحقاً');
  return [];
};

export const paySalary = (employeeId: string, amount: number, month: string) => {
  console.log('paySalary - سيتم تنفيذه لاحقاً');
};

// ===== الخدمات المحسنة الجديدة =====
// استخدام الخدمات الجديدة بدلاً من الدوال القديمة

// إعادة تصدير الخدمات الجديدة للتوافق مع الكود الموجود
export { MaterialService } from '@/services/databaseService';
export { ProjectService } from '@/services/projectService';
export { CustomerService } from '@/services/customerService';

// دوال مساعدة محسنة للتوافق مع الكود الموجود
export const getMaterials = async (): Promise<Material[]> => {
  try {
    const { MaterialService } = await import('@/services/databaseService');
    return await MaterialService.getAll();
  } catch (error) {
    console.error('خطأ في جلب المواد:', error);
    return [];
  }
};

export const getProjects = async (): Promise<Project[]> => {
  try {
    const { ProjectService } = await import('@/services/projectService');
    return await ProjectService.getAll();
  } catch (error) {
    console.error('خطأ في جلب المشاريع:', error);
    return [];
  }
};

export const getCustomers = async (): Promise<Customer[]> => {
  try {
    const { CustomerService } = await import('@/services/customerService');
    return await CustomerService.getAll();
  } catch (error) {
    console.error('خطأ في جلب العملاء:', error);
    return [];
  }
};