// نظام الألوان والتصميم الموحد للتطبيق
export const theme = {
  // الألوان الأساسية
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    secondary: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
    },
    accent: {
      50: '#fef7ff',
      100: '#fceeff',
      200: '#f8d4fe',
      300: '#f2b1fc',
      400: '#e879f9',
      500: '#d946ef',
      600: '#c026d3',
      700: '#a21caf',
      800: '#86198f',
      900: '#701a75',
    },
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
    },
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
    }
  },

  // التدرجات الموحدة
  gradients: {
    primary: 'bg-gradient-to-r from-blue-600 to-blue-700',
    secondary: 'bg-gradient-to-r from-green-600 to-green-700',
    accent: 'bg-gradient-to-r from-purple-600 to-purple-700',
    success: 'bg-gradient-to-r from-green-500 to-emerald-600',
    warning: 'bg-gradient-to-r from-yellow-500 to-orange-600',
    error: 'bg-gradient-to-r from-red-500 to-red-600',
    info: 'bg-gradient-to-r from-blue-500 to-cyan-600',
    
    // تدرجات خاصة للخلفيات
    backgroundPrimary: 'bg-gradient-to-br from-blue-50 via-white to-green-50',
    backgroundSecondary: 'bg-gradient-to-br from-gray-50 via-white to-blue-50',
    backgroundAccent: 'bg-gradient-to-br from-purple-50 via-white to-pink-50',
    
    // تدرجات للبطاقات
    cardPrimary: 'bg-gradient-to-r from-blue-600 to-green-600',
    cardSecondary: 'bg-gradient-to-r from-purple-600 to-blue-600',
    cardAccent: 'bg-gradient-to-r from-orange-600 to-red-600',
    cardSuccess: 'bg-gradient-to-r from-green-600 to-emerald-600',
    cardWarning: 'bg-gradient-to-r from-yellow-600 to-orange-600',
    cardError: 'bg-gradient-to-r from-red-600 to-pink-600',
  },

  // المسافات والهوامش الموحدة
  spacing: {
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '1rem',       // 16px
    lg: '1.5rem',     // 24px
    xl: '2rem',       // 32px
    '2xl': '3rem',    // 48px
    '3xl': '4rem',    // 64px
    '4xl': '6rem',    // 96px
    '5xl': '8rem',    // 128px
  },

  // أحجام الخطوط الموحدة
  typography: {
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem', // 60px
    },
    fontWeight: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75',
    }
  },

  // الظلال الموحدة
  shadows: {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
    '2xl': 'shadow-2xl',
    inner: 'shadow-inner',
    none: 'shadow-none',
  },

  // الحدود والزوايا
  borders: {
    radius: {
      none: '0',
      sm: '0.125rem',   // 2px
      md: '0.375rem',   // 6px
      lg: '0.5rem',     // 8px
      xl: '0.75rem',    // 12px
      '2xl': '1rem',    // 16px
      '3xl': '1.5rem',  // 24px
      full: '9999px',
    },
    width: {
      0: '0',
      1: '1px',
      2: '2px',
      4: '4px',
      8: '8px',
    }
  },

  // نقاط الكسر للاستجابة
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // الانتقالات والحركات
  transitions: {
    duration: {
      75: '75ms',
      100: '100ms',
      150: '150ms',
      200: '200ms',
      300: '300ms',
      500: '500ms',
      700: '700ms',
      1000: '1000ms',
    },
    timing: {
      linear: 'linear',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    }
  },

  // أحجام المكونات
  components: {
    button: {
      height: {
        sm: '2rem',      // 32px
        md: '2.5rem',    // 40px
        lg: '3rem',      // 48px
        xl: '3.5rem',    // 56px
      },
      padding: {
        sm: 'px-3 py-1.5',
        md: 'px-4 py-2',
        lg: 'px-6 py-3',
        xl: 'px-8 py-4',
      }
    },
    input: {
      height: {
        sm: '2rem',      // 32px
        md: '2.5rem',    // 40px
        lg: '3rem',      // 48px
      }
    },
    card: {
      padding: {
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
        xl: 'p-10',
      }
    }
  }
};

// دوال مساعدة للوصول للثيم
export const getColor = (color: string, shade: number = 500) => {
  const colorPath = color.split('.');
  let result: any = theme.colors;
  
  for (const path of colorPath) {
    result = result[path];
  }
  
  return result[shade] || result;
};

export const getGradient = (gradientName: string) => {
  return theme.gradients[gradientName as keyof typeof theme.gradients] || '';
};

export const getSpacing = (size: string) => {
  return theme.spacing[size as keyof typeof theme.spacing] || size;
};

export const getFontSize = (size: string) => {
  return theme.typography.fontSize[size as keyof typeof theme.typography.fontSize] || size;
};

export const getShadow = (size: string) => {
  return theme.shadows[size as keyof typeof theme.shadows] || '';
};

export const getBorderRadius = (size: string) => {
  return theme.borders.radius[size as keyof typeof theme.borders.radius] || size;
};

// كلاسات CSS مساعدة
export const themeClasses = {
  // خلفيات الصفحات
  pageBackground: 'min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50',
  
  // البطاقات
  card: 'rounded-lg border bg-card text-card-foreground shadow-sm',
  cardHeader: 'flex flex-col space-y-1.5 p-6',
  cardContent: 'p-6 pt-0',
  
  // الأزرار
  buttonPrimary: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white',
  buttonSecondary: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white',
  buttonOutline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
  
  // النصوص
  heading1: 'text-4xl font-bold text-gray-800',
  heading2: 'text-3xl font-bold text-gray-800',
  heading3: 'text-2xl font-semibold text-gray-800',
  heading4: 'text-xl font-semibold text-gray-700',
  bodyText: 'text-base text-gray-600',
  smallText: 'text-sm text-gray-500',
  
  // الحاويات
  container: 'container mx-auto px-4',
  section: 'py-8',
  
  // الشبكات
  gridCols2: 'grid grid-cols-1 md:grid-cols-2 gap-6',
  gridCols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
  gridCols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6',
  
  // الفلكس
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  flexStart: 'flex items-center justify-start',
  flexEnd: 'flex items-center justify-end',
  
  // الانتقالات
  transition: 'transition-all duration-200 ease-in-out',
  transitionFast: 'transition-all duration-150 ease-in-out',
  transitionSlow: 'transition-all duration-300 ease-in-out',
};
