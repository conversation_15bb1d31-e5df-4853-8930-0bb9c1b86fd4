// سياق التطبيق العام لإدارة الحالة
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useNotifications } from '@/components/common/NotificationSystem';
import { useConfirmation } from '@/components/common/ConfirmationDialog';
import { handleError, handleSuccess } from '@/utils/commonUtils';

// أنواع البيانات
interface AppStats {
  totalProjects: number;
  activeProjects: number;
  totalMaterials: number;
  totalEmployees: number;
  currentBalance: number;
  monthlyRevenue: number;
  pendingPayments: number;
}

interface AppSettings {
  currency: string;
  taxRate: number;
  profitMarginRate: number;
  companyName: string;
  companyPhone: string;
  companyAddress: string;
  theme: 'light' | 'dark';
  language: 'ar' | 'en';
}

interface AppState {
  loading: boolean;
  stats: AppStats;
  settings: AppSettings;
  user: {
    name: string;
    role: string;
    permissions: string[];
  } | null;
  lastUpdated: Date | null;
}

// أنواع الإجراءات
type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_STATS'; payload: AppStats }
  | { type: 'SET_SETTINGS'; payload: AppSettings }
  | { type: 'SET_USER'; payload: AppState['user'] }
  | { type: 'UPDATE_LAST_UPDATED' }
  | { type: 'RESET_STATE' };

// الحالة الأولية
const initialState: AppState = {
  loading: false,
  stats: {
    totalProjects: 0,
    activeProjects: 0,
    totalMaterials: 0,
    totalEmployees: 0,
    currentBalance: 0,
    monthlyRevenue: 0,
    pendingPayments: 0,
  },
  settings: {
    currency: 'د.ل',
    taxRate: 0.1,
    profitMarginRate: 0.2,
    companyName: 'مصنع الأثاث',
    companyPhone: '',
    companyAddress: '',
    theme: 'light',
    language: 'ar',
  },
  user: {
    name: 'المدير',
    role: 'admin',
    permissions: ['read', 'write', 'delete', 'admin'],
  },
  lastUpdated: null,
};

// مخفض الحالة
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_STATS':
      return { 
        ...state, 
        stats: action.payload,
        lastUpdated: new Date()
      };
    
    case 'SET_SETTINGS':
      return { 
        ...state, 
        settings: action.payload,
        lastUpdated: new Date()
      };
    
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    case 'UPDATE_LAST_UPDATED':
      return { ...state, lastUpdated: new Date() };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
};

// واجهة السياق
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  
  // دوال مساعدة
  updateStats: (stats: Partial<AppStats>) => void;
  updateSettings: (settings: Partial<AppSettings>) => void;
  setLoading: (loading: boolean) => void;
  refreshData: () => Promise<void>;
  
  // نظام الإشعارات
  notifications: ReturnType<typeof useNotifications>;
  
  // نظام التأكيدات
  confirmation: ReturnType<typeof useConfirmation>;
  
  // دوال الأذونات
  hasPermission: (permission: string) => boolean;
  isAdmin: () => boolean;
}

// إنشاء السياق
const AppContext = createContext<AppContextType | undefined>(undefined);

// مزود السياق
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const notifications = useNotifications();
  const confirmation = useConfirmation();

  // تحميل البيانات الأولية
  useEffect(() => {
    loadInitialData();
  }, []);

  // تحميل البيانات من التخزين المحلي
  useEffect(() => {
    const savedSettings = localStorage.getItem('app-settings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        dispatch({ type: 'SET_SETTINGS', payload: { ...state.settings, ...settings } });
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }, []);

  // حفظ الإعدادات في التخزين المحلي
  useEffect(() => {
    localStorage.setItem('app-settings', JSON.stringify(state.settings));
  }, [state.settings]);

  const loadInitialData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // محاكاة تحميل البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // يمكن استبدال هذا بتحميل البيانات الفعلية
      const mockStats: AppStats = {
        totalProjects: 25,
        activeProjects: 8,
        totalMaterials: 45,
        totalEmployees: 12,
        currentBalance: 15000,
        monthlyRevenue: 8500,
        pendingPayments: 3200,
      };
      
      dispatch({ type: 'SET_STATS', payload: mockStats });
      
    } catch (error) {
      handleError(error, 'تحميل البيانات الأولية');
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const updateStats = (newStats: Partial<AppStats>) => {
    dispatch({ 
      type: 'SET_STATS', 
      payload: { ...state.stats, ...newStats } 
    });
  };

  const updateSettings = (newSettings: Partial<AppSettings>) => {
    dispatch({ 
      type: 'SET_SETTINGS', 
      payload: { ...state.settings, ...newSettings } 
    });
    handleSuccess('تم حفظ الإعدادات بنجاح');
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const refreshData = async () => {
    try {
      setLoading(true);
      await loadInitialData();
      notifications.showSuccess('تم تحديث البيانات', 'تم تحديث جميع البيانات بنجاح');
    } catch (error) {
      handleError(error, 'تحديث البيانات');
    } finally {
      setLoading(false);
    }
  };

  const hasPermission = (permission: string): boolean => {
    return state.user?.permissions.includes(permission) || false;
  };

  const isAdmin = (): boolean => {
    return state.user?.role === 'admin' || false;
  };

  const contextValue: AppContextType = {
    state,
    dispatch,
    updateStats,
    updateSettings,
    setLoading,
    refreshData,
    notifications,
    confirmation,
    hasPermission,
    isAdmin,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
      {/* مكون التأكيدات */}
      <confirmation.ConfirmationComponent />
    </AppContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Hook للإحصائيات
export const useStats = () => {
  const { state, updateStats } = useApp();
  return {
    stats: state.stats,
    updateStats,
    loading: state.loading,
    lastUpdated: state.lastUpdated,
  };
};

// Hook للإعدادات
export const useSettings = () => {
  const { state, updateSettings } = useApp();
  return {
    settings: state.settings,
    updateSettings,
  };
};

// Hook للمستخدم والأذونات
export const useAuth = () => {
  const { state, hasPermission, isAdmin } = useApp();
  return {
    user: state.user,
    hasPermission,
    isAdmin,
  };
};

export default AppContext;
