import { DetailedMaterial, MaterialCategory, Worker, Factory, Designer } from '../types/materials';

export const seedMaterialCategories: MaterialCategory[] = [
  {
    id: 'cat1',
    name: 'الأخشاب',
    description: 'أنواع مختلفة من الأخشاب المستخدمة في التصنيع',
    color: '#FF5733',
  },
  {
    id: 'cat2',
    name: 'المعادن',
    description: 'معادن متنوعة للأجزاء الهيكلية والزخرفية',
    color: '#33FF57',
  },
  {
    id: 'cat3',
    name: 'الأقمشة',
    description: 'أقمشة للتنجيد والتشطيبات',
    color: '#3357FF',
  },
  {
    id: 'cat4',
    name: 'المواد الكيميائية',
    description: 'دهانات، غراء، ومواد تشطيب',
    color: '#FF33F0',
  },
];

export const DEFAULT_MATERIALS: DetailedMaterial[] = [
  // الخامات الأساسية
  {
    id: 'mat1',
    code: 'WOOD001',
    name: 'خشب زان',
    description: 'خشب زان عالي الجودة',
    category: seedMaterialCategories[0],
    unit: 'متر مكعب',
    purchasePrice: 1500,
    salePrice: 2000,
    availableQuantity: 10,
    minQuantity: 2,
    supplier: 'المورد الأول',
    notes: 'يستخدم للأثاث الفاخر',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'mat2',
    code: 'METAL001',
    name: 'حديد مشغول',
    description: 'حديد مشغول للزينة',
    category: seedMaterialCategories[1],
    unit: 'كيلو جرام',
    purchasePrice: 50,
    salePrice: 75,
    availableQuantity: 100,
    minQuantity: 10,
    supplier: 'المورد الثاني',
    notes: 'للتطبيقات الخارجية والداخلية',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'mat3',
    code: 'FABRIC001',
    name: 'قماش مخمل',
    description: 'قماش مخمل فاخر للتنجيد',
    category: seedMaterialCategories[2],
    unit: 'متر طولي',
    purchasePrice: 120,
    salePrice: 180,
    availableQuantity: 50,
    minQuantity: 5,
    supplier: 'المورد الثالث',
    notes: 'متوفر بألوان متعددة',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'mat4',
    code: 'CHEM001',
    name: 'دهان بولي يوريثان',
    description: 'دهان عالي المتانة',
    category: seedMaterialCategories[3],
    unit: 'لتر',
    purchasePrice: 80,
    salePrice: 120,
    availableQuantity: 20,
    minQuantity: 3,
    supplier: 'المورد الرابع',
    notes: 'مقاوم للخدوش والرطوبة',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

export const seedWorkers: Worker[] = [
  {
    id: 'worker1',
    name: 'أحمد النجار',
    specialty: 'نجارة',
    pricePerSqm: 50,
    phone: '0501234567',
  },
  {
    id: 'worker2',
    name: 'محمد الحداد',
    specialty: 'حدادة',
    pricePerSqm: 60,
    phone: '0507654321',
  },
];

export const seedFactories: Factory[] = [
  {
    id: 'factory1',
    name: 'مصنع الأثاث الحديث',
    specialty: 'تصنيع الأثاث الخشبي',
    pricePerSqm: 100,
    location: 'المنطقة الصناعية الأولى',
  },
  {
    id: 'factory2',
    name: 'مصنع المعادن المبتكر',
    specialty: 'تصنيع الأجزاء المعدنية',
    pricePerSqm: 120,
    location: 'المنطقة الصناعية الثانية',
  },
];

export const seedDesigners: Designer[] = [
  {
    id: 'designer1',
    name: 'سارة الديكور',
    specialty: 'تصميم داخلي',
    pricePerSqm: 70,
    phone: '0551122334',
  },
  {
    id: 'designer2',
    name: 'خالد التصميم',
    specialty: 'تصميم أثاث',
    pricePerSqm: 80,
    phone: '0554433221',
  },
];