// خدمة إدارة المواد التفصيلية
import { 
  DetailedMaterial, 
  MaterialCategory, 
  ProjectMaterial, 
  MaterialCalculation,
  ProjectMaterialsSummary,
  DEFAULT_MATERIAL_CATEGORIES,
  DEFAULT_MATERIALS
} from '@/types/materials';

// التحقق من وجود Electron API
const isElectron = typeof window !== 'undefined' && window.electronAPI;

// خدمات فئات المواد
export const getMaterialCategories = async (): Promise<MaterialCategory[]> => {
  if (isElectron) {
    try {
      return await window.electronAPI.getMaterialCategories();
    } catch (error) {
      console.error('خطأ في جلب فئات المواد:', error);
      return DEFAULT_MATERIAL_CATEGORIES;
    }
  }
  return DEFAULT_MATERIAL_CATEGORIES;
};

export const addMaterialCategory = async (category: Omit<MaterialCategory, 'id'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addMaterialCategory(category);
    } catch (error) {
      console.error('خطأ في إضافة فئة المواد:', error);
      return null;
    }
  }
  return null;
};

// خدمات المواد التفصيلية
export const getDetailedMaterials = async (): Promise<DetailedMaterial[]> => {
  if (isElectron) {
    try {
      return await window.electronAPI.getDetailedMaterials();
    } catch (error) {
      console.error('خطأ في جلب المواد التفصيلية:', error);
      return [];
    }
  }
  return [];
};

export const getDetailedMaterialsByCategory = async (categoryId: string): Promise<DetailedMaterial[]> => {
  if (isElectron) {
    try {
      return await window.electronAPI.getDetailedMaterialsByCategory(categoryId);
    } catch (error) {
      console.error('خطأ في جلب المواد حسب الفئة:', error);
      return [];
    }
  }
  return [];
};

export const addDetailedMaterial = async (material: Omit<DetailedMaterial, 'id' | 'createdAt' | 'updatedAt'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addDetailedMaterial(material);
    } catch (error) {
      console.error('خطأ في إضافة المادة:', error);
      return null;
    }
  }
  return null;
};

export const updateDetailedMaterial = async (id: string, material: Partial<DetailedMaterial>): Promise<boolean> => {
  if (isElectron) {
    try {
      return await window.electronAPI.updateDetailedMaterial(id, material);
    } catch (error) {
      console.error('خطأ في تحديث المادة:', error);
      return false;
    }
  }
  return false;
};

export const deleteDetailedMaterial = async (id: string): Promise<boolean> => {
  if (isElectron) {
    try {
      return await window.electronAPI.deleteDetailedMaterial(id);
    } catch (error) {
      console.error('خطأ في حذف المادة:', error);
      return false;
    }
  }
  return false;
};

// خدمات مواد المشاريع
export const getProjectMaterials = async (projectId: string): Promise<ProjectMaterial[]> => {
  if (isElectron) {
    try {
      return await window.electronAPI.getProjectMaterials(projectId);
    } catch (error) {
      console.error('خطأ في جلب مواد المشروع:', error);
      return [];
    }
  }
  return [];
};

export const addProjectMaterial = async (projectMaterial: Omit<ProjectMaterial, 'id' | 'createdAt' | 'updatedAt'>): Promise<string | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.addProjectMaterial(projectMaterial);
    } catch (error) {
      console.error('خطأ في إضافة مادة للمشروع:', error);
      return null;
    }
  }
  return null;
};

export const updateProjectMaterial = async (id: string, updates: Partial<ProjectMaterial>): Promise<boolean> => {
  if (isElectron) {
    try {
      return await window.electronAPI.updateProjectMaterial(id, updates);
    } catch (error) {
      console.error('خطأ في تحديث مادة المشروع:', error);
      return false;
    }
  }
  return false;
};

export const deleteProjectMaterial = async (id: string): Promise<boolean> => {
  if (isElectron) {
    try {
      return await window.electronAPI.deleteProjectMaterial(id);
    } catch (error) {
      console.error('خطأ في حذف مادة المشروع:', error);
      return false;
    }
  }
  return false;
};

// حسابات المواد
export const calculateMaterialCosts = (materials: MaterialCalculation[]): ProjectMaterialsSummary => {
  // التأكد من أن materials مصفوفة صحيحة
  const safeMaterials = Array.isArray(materials) ? materials : [];

  const totalPurchaseCost = safeMaterials.reduce((sum, material) => {
    const purchase = material.totalPurchase || 0;
    return sum + purchase;
  }, 0);

  const totalSaleCost = safeMaterials.reduce((sum, material) => {
    const sale = material.totalSale || 0;
    return sum + sale;
  }, 0);

  const totalProfit = totalSaleCost - totalPurchaseCost;

  const externalCost = safeMaterials
    .filter(m => m.source === 'external')
    .reduce((sum, material) => {
      const purchase = material.totalPurchase || 0;
      return sum + purchase;
    }, 0);

  const stockCost = safeMaterials
    .filter(m => m.source === 'stock')
    .reduce((sum, material) => {
      const purchase = material.totalPurchase || 0;
      return sum + purchase;
    }, 0);

  return {
    projectId: '',
    materials: safeMaterials,
    totalPurchaseCost,
    totalSaleCost,
    totalProfit,
    externalCost,
    stockCost
  };
};

// البحث والفلترة
export const searchMaterials = async (query: string): Promise<DetailedMaterial[]> => {
  if (isElectron) {
    try {
      return await window.electronAPI.searchDetailedMaterials(query);
    } catch (error) {
      console.error('خطأ في البحث عن المواد:', error);
      return [];
    }
  }
  return [];
};

// تهيئة البيانات الافتراضية
export const initializeDefaultData = async (): Promise<boolean> => {
  if (isElectron) {
    try {
      // إضافة فئات المواد الافتراضية
      for (const category of DEFAULT_MATERIAL_CATEGORIES) {
        await addMaterialCategory(category);
      }
      
      // إضافة المواد الافتراضية
      for (const material of DEFAULT_MATERIALS) {
        await addDetailedMaterial(material);
      }
      
      return true;
    } catch (error) {
      console.error('خطأ في تهيئة البيانات الافتراضية:', error);
      return false;
    }
  }
  return false;
};

// تحديث كمية المادة في المخزن
export const updateMaterialStock = async (materialId: string, quantityChange: number): Promise<boolean> => {
  if (isElectron) {
    try {
      return await window.electronAPI.updateMaterialStock(materialId, quantityChange);
    } catch (error) {
      console.error('خطأ في تحديث المخزن:', error);
      return false;
    }
  }
  return false;
};

// التحقق من توفر الكمية المطلوبة
export const checkMaterialAvailability = async (materialId: string, requiredQuantity: number): Promise<boolean> => {
  if (isElectron) {
    try {
      const material = await window.electronAPI.getDetailedMaterial(materialId);
      return material && material.availableQuantity >= requiredQuantity;
    } catch (error) {
      console.error('خطأ في التحقق من توفر المادة:', error);
      return false;
    }
  }
  return false;
};

// حفظ مواد المشروع في قاعدة البيانات
export const saveProjectMaterials = async (projectId: string, materialsSummary: ProjectMaterialsSummary): Promise<boolean> => {
  if (isElectron && materialsSummary.materials.length > 0) {
    try {
      // حذف المواد القديمة للمشروع (في حالة التحديث)
      const existingMaterials = await getProjectMaterials(projectId);
      for (const material of existingMaterials) {
        await deleteProjectMaterial(material.id);
      }

      // إضافة المواد الجديدة
      for (const material of materialsSummary.materials) {
        const projectMaterial = {
          projectId,
          materialId: material.materialId,
          material: await getDetailedMaterial(material.materialId), // Add the material property
          requiredQuantity: material.requiredQuantity,
          usedQuantity: 0,
          totalPurchaseCost: material.totalPurchase,
          totalSaleCost: material.totalSale,
          profit: material.profit,
          source: material.source,
          notes: '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        const id = await addProjectMaterial(projectMaterial);
        if (!id) {
          throw new Error(`فشل في حفظ المادة: ${material.materialName}`);
        }

        // تحديث المخزن إذا كانت المادة من المخزن
        if (material.source === 'stock') {
          await updateMaterialStock(material.materialId, -material.requiredQuantity);
        }
      }

      return true;
    } catch (error) {
      console.error('خطأ في حفظ مواد المشروع:', error);
      return false;
    }
  }
  return true; // إذا لم توجد مواد، اعتبر العملية ناجحة
};

// إضافة دورة حياة تحديث الكميات
// تم نقل هذا الكود إلى مكون MaterialStockMonitor
// useEffect(() => {
//   const updateMaterialStock = async () => {
//     const materials = await fetchMaterials();
//     materials.forEach(material => {
//       if(material.quantity < material.minThreshold) {
//         showToast(`نقص في المخزون: ${material.name}`);
//       }
//     });
//   };
//   updateMaterialStock();
// }, [projectId]);

// الحصول على مادة تفصيلية محددة
export const getDetailedMaterial = async (id: string): Promise<DetailedMaterial | null> => {
  if (isElectron) {
    try {
      return await window.electronAPI.getDetailedMaterial(id);
    } catch (error) {
      console.error('خطأ في جلب المادة التفصيلية:', error);
      return null;
    }
  }
  return null;
};
