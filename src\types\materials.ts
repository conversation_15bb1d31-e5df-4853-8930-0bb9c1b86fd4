// أنواع البيانات لنظام المواد المتقدم

export interface DetailedMaterial {
  id: string;
  code: string; // رقم المادة مثل H-1, H-2
  name: string;
  description?: string;
  category: MaterialCategory;
  unit: string; // الوحدة (قطعة، متر، كيلو، إلخ)
  purchasePrice: number; // سعر الشراء
  salePrice: number; // سعر البيع
  availableQuantity: number; // الكمية المتوفرة في المخزن
  minQuantity: number; // الحد الأدنى للكمية
  supplier?: string; // المورد
  notes?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MaterialCategory {
  id: string;
  name: string;
  description?: string;
  color: string; // لون للتمييز في الواجهة
}

export interface ProjectMaterial {
  id: string;
  projectId: string;
  materialId: string;
  material: DetailedMaterial;
  requiredQuantity: number;
  usedQuantity: number;
  totalPurchaseCost: number; // الكمية × سعر الشراء
  totalSaleCost: number; // الكمية × سعر البيع
  profit: number; // الفرق بين البيع والشراء
  source: 'external' | 'stock'; // خارجي أم من المخزن
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MaterialCalculation {
  materialId: string;
  materialName: string;
  materialCode: string;
  requiredQuantity: number;
  purchasePrice: number;
  salePrice: number;
  totalPurchase: number;
  totalSale: number;
  profit: number;
  source: 'external' | 'stock';
}

export interface ProjectMaterialsSummary {
  projectId: string;
  materials: MaterialCalculation[];
  totalPurchaseCost: number;
  totalSaleCost: number;
  totalProfit: number;
  externalCost: number; // التكلفة الخارجية
  stockCost: number; // تكلفة المخزن
}

export interface Worker {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  phone: string;
}

export interface Factory {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  location: string;
}

export interface Designer {
  id: string;
  name: string;
  specialty: string;
  pricePerSqm: number;
  phone: string;
}

// فئات المواد الافتراضية
export const DEFAULT_MATERIAL_CATEGORIES: MaterialCategory[] = [
  {
    id: 'basic-materials',
    name: 'الخامات الأساسية',
    description: 'MDF, AGT, القلعة، الخشب الطبيعي',
    color: '#3B82F6'
  },
  {
    id: 'accessories',
    name: 'الإكسسوارات والتجهيزات',
    description: 'H-1 إلى H-66، المفصلات، المقابض، القضبان',
    color: '#10B981'
  },
  {
    id: 'paint-materials',
    name: 'مواد الطلاء',
    description: 'سيلر، دوكو، بوليستر، ورنيش',
    color: '#F59E0B'
  },
  {
    id: 'tools',
    name: 'الأدوات والمعدات',
    description: 'براغي، مسامير، غراء، أدوات القطع',
    color: '#EF4444'
  },
  {
    id: 'glass-mirrors',
    name: 'الزجاج والمرايا',
    description: 'زجاج عادي، زجاج مقسى، مرايا',
    color: '#8B5CF6'
  }
];

// مواد افتراضية للنظام
export const DEFAULT_MATERIALS: Omit<DetailedMaterial, 'id' | 'createdAt' | 'updatedAt'>[] = [
  // الخامات الأساسية
  {
    code: 'MDF-18',
    name: 'MDF سماكة 18 مم',
    category: DEFAULT_MATERIAL_CATEGORIES[0],
    unit: 'متر مربع',
    purchasePrice: 45.00,
    salePrice: 65.00,
    availableQuantity: 100,
    minQuantity: 20,
    supplier: 'شركة الخشب الليبية',
    isActive: true
  },
  {
    code: 'AGT-001',
    name: 'AGT أبيض لامع',
    category: DEFAULT_MATERIAL_CATEGORIES[0],
    unit: 'متر مربع',
    purchasePrice: 85.00,
    salePrice: 120.00,
    availableQuantity: 50,
    minQuantity: 10,
    supplier: 'مستورد تركي',
    isActive: true
  },
  {
    code: 'WOOD-001',
    name: 'خشب زان طبيعي',
    category: DEFAULT_MATERIAL_CATEGORIES[0],
    unit: 'متر مربع',
    purchasePrice: 150.00,
    salePrice: 200.00,
    availableQuantity: 25,
    minQuantity: 5,
    supplier: 'مستورد أوروبي',
    isActive: true
  },

  // الإكسسوارات
  {
    code: 'H-1',
    name: 'مفصلة عادية 35 مم',
    category: DEFAULT_MATERIAL_CATEGORIES[1],
    unit: 'قطعة',
    purchasePrice: 2.50,
    salePrice: 4.00,
    availableQuantity: 500,
    minQuantity: 100,
    supplier: 'شركة الإكسسوارات',
    isActive: true
  },
  {
    code: 'H-2',
    name: 'مفصلة هيدروليك',
    category: DEFAULT_MATERIAL_CATEGORIES[1],
    unit: 'قطعة',
    purchasePrice: 8.00,
    salePrice: 12.00,
    availableQuantity: 200,
    minQuantity: 50,
    supplier: 'شركة الإكسسوارات',
    isActive: true
  },
  {
    code: 'H-10',
    name: 'مقبض ألمنيوم',
    category: DEFAULT_MATERIAL_CATEGORIES[1],
    unit: 'قطعة',
    purchasePrice: 5.00,
    salePrice: 8.00,
    availableQuantity: 300,
    minQuantity: 50,
    supplier: 'شركة الإكسسوارات',
    isActive: true
  },

  // مواد الطلاء
  {
    code: 'SEALER-001',
    name: 'سيلر شفاف',
    category: DEFAULT_MATERIAL_CATEGORIES[2],
    unit: 'لتر',
    purchasePrice: 25.00,
    salePrice: 35.00,
    availableQuantity: 30,
    minQuantity: 10,
    supplier: 'شركة الدهانات',
    isActive: true
  },
  {
    code: 'DUCO-WHITE',
    name: 'دوكو أبيض',
    category: DEFAULT_MATERIAL_CATEGORIES[2],
    unit: 'لتر',
    purchasePrice: 30.00,
    salePrice: 45.00,
    availableQuantity: 25,
    minQuantity: 8,
    supplier: 'شركة الدهانات',
    isActive: true
  }
];
