// خدمة العملاء الموحدة
// تهدف لتوحيد جميع عمليات إدارة العملاء

import { runQuery, getQuery, allQuery } from '../utils/database';
import { Customer } from '../utils/dataManager';
import { measureFunction } from './performanceMonitor';
import { handleError, ErrorType } from './errorHandler';
import { 
  ensureArray, 
  validateId, 
  validatePhone,
  validateEmail,
  safeReduce 
} from '@/utils/dataValidation';

// تحقق من صحة بيانات العميل
const validateCustomerData = (customer: any) => {
  return {
    ...customer,
    name: customer.name?.trim() || '',
    phone: validatePhone(customer.phone) || '',
    email: validateEmail(customer.email) || '',
    address: customer.address?.trim() || '',
    notes: customer.notes?.trim() || ''
  };
};

export class CustomerService {
  // جلب جميع العملاء
  static async getAll(): Promise<Customer[]> {
    return measureFunction(async () => {
      try {
        const customers = await allQuery(`
          SELECT c.*, 
                 COUNT(p.id) as totalProjects,
                 COALESCE(SUM(p.totalCost), 0) as totalSpent,
                 COALESCE(SUM(p.paidAmount), 0) as totalPaid,
                 COALESCE(SUM(p.remainingAmount), 0) as totalRemaining
          FROM customers c
          LEFT JOIN projects p ON c.id = p.customerId
          GROUP BY c.id
          ORDER BY c.name
        `);
        return ensureArray(customers).map(validateCustomerData);
      } catch (error) {
        handleError(error, 'جلب جميع العملاء', ErrorType.DATABASE);
        return [];
      }
    }, 'Get All Customers', 'database');
  }

  // جلب عميل بالمعرف
  static async getById(id: string): Promise<Customer | null> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) return null;
        
        const customer = await getQuery(`
          SELECT c.*, 
                 COUNT(p.id) as totalProjects,
                 COALESCE(SUM(p.totalCost), 0) as totalSpent,
                 COALESCE(SUM(p.paidAmount), 0) as totalPaid,
                 COALESCE(SUM(p.remainingAmount), 0) as totalRemaining
          FROM customers c
          LEFT JOIN projects p ON c.id = p.customerId
          WHERE c.id = ?
          GROUP BY c.id
        `, [validId]);
        
        return customer ? validateCustomerData(customer) : null;
      } catch (error) {
        handleError(error, 'جلب عميل بالمعرف', ErrorType.DATABASE);
        return null;
      }
    }, 'Get Customer By ID', 'database', { id });
  }

  // البحث عن عميل بالهاتف
  static async getByPhone(phone: string): Promise<Customer | null> {
    return measureFunction(async () => {
      try {
        const validPhone = validatePhone(phone);
        if (!validPhone) return null;
        
        const customer = await getQuery(`
          SELECT c.*, 
                 COUNT(p.id) as totalProjects,
                 COALESCE(SUM(p.totalCost), 0) as totalSpent,
                 COALESCE(SUM(p.paidAmount), 0) as totalPaid,
                 COALESCE(SUM(p.remainingAmount), 0) as totalRemaining
          FROM customers c
          LEFT JOIN projects p ON c.id = p.customerId
          WHERE c.phone = ?
          GROUP BY c.id
        `, [validPhone]);
        
        return customer ? validateCustomerData(customer) : null;
      } catch (error) {
        handleError(error, 'البحث عن عميل بالهاتف', ErrorType.DATABASE);
        return null;
      }
    }, 'Get Customer By Phone', 'database', { phone });
  }

  // إنشاء عميل جديد
  static async create(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    return measureFunction(async () => {
      try {
        const validatedCustomer = validateCustomerData(customer);
        
        // التحقق من عدم وجود عميل بنفس الهاتف
        if (validatedCustomer.phone) {
          const existingCustomer = await this.getByPhone(validatedCustomer.phone);
          if (existingCustomer) {
            throw new Error('يوجد عميل بنفس رقم الهاتف');
          }
        }
        
        const id = `cust_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        await runQuery(`
          INSERT INTO customers (
            id, name, phone, email, address, notes, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          id,
          validatedCustomer.name,
          validatedCustomer.phone,
          validatedCustomer.email,
          validatedCustomer.address,
          validatedCustomer.notes,
          new Date().toISOString(),
          new Date().toISOString()
        ]);
        
        return id;
      } catch (error) {
        handleError(error, 'إنشاء عميل جديد', ErrorType.DATABASE);
        throw error;
      }
    }, 'Create Customer', 'database');
  }

  // تحديث عميل
  static async update(id: string, customer: Partial<Customer>): Promise<void> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) throw new Error('معرف العميل غير صحيح');
        
        const validatedCustomer = validateCustomerData(customer);
        
        // التحقق من عدم تضارب رقم الهاتف
        if (validatedCustomer.phone) {
          const existingCustomer = await this.getByPhone(validatedCustomer.phone);
          if (existingCustomer && existingCustomer.id !== validId) {
            throw new Error('يوجد عميل آخر بنفس رقم الهاتف');
          }
        }
        
        const fields = Object.keys(validatedCustomer).filter(
          key => key !== 'id' && key !== 'createdAt' && validatedCustomer[key as keyof Customer] !== undefined
        );
        
        if (fields.length === 0) return;
        
        const values = fields.map(key => validatedCustomer[key as keyof Customer]);
        const setClause = fields.map(field => `${field} = ?`).join(', ');
        
        await runQuery(
          `UPDATE customers SET ${setClause}, updatedAt = ? WHERE id = ?`,
          [...values, new Date().toISOString(), validId]
        );
      } catch (error) {
        handleError(error, 'تحديث العميل', ErrorType.DATABASE);
        throw error;
      }
    }, 'Update Customer', 'database', { id });
  }

  // حذف عميل
  static async delete(id: string): Promise<void> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) throw new Error('معرف العميل غير صحيح');
        
        // التحقق من عدم وجود مشاريع مرتبطة
        const projects = await allQuery('SELECT id FROM projects WHERE customerId = ?', [validId]);
        if (projects && projects.length > 0) {
          throw new Error('لا يمكن حذف العميل لوجود مشاريع مرتبطة به');
        }
        
        await runQuery('DELETE FROM customers WHERE id = ?', [validId]);
      } catch (error) {
        handleError(error, 'حذف العميل', ErrorType.DATABASE);
        throw error;
      }
    }, 'Delete Customer', 'database', { id });
  }

  // البحث في العملاء
  static async search(query: string): Promise<Customer[]> {
    return measureFunction(async () => {
      try {
        const searchTerm = `%${query.toLowerCase()}%`;
        const customers = await allQuery(`
          SELECT c.*, 
                 COUNT(p.id) as totalProjects,
                 COALESCE(SUM(p.totalCost), 0) as totalSpent,
                 COALESCE(SUM(p.paidAmount), 0) as totalPaid,
                 COALESCE(SUM(p.remainingAmount), 0) as totalRemaining
          FROM customers c
          LEFT JOIN projects p ON c.id = p.customerId
          WHERE LOWER(c.name) LIKE ? 
          OR c.phone LIKE ?
          OR LOWER(c.email) LIKE ?
          OR LOWER(c.address) LIKE ?
          GROUP BY c.id
          ORDER BY c.name
        `, [searchTerm, `%${query}%`, searchTerm, searchTerm]);
        
        return ensureArray(customers).map(validateCustomerData);
      } catch (error) {
        handleError(error, 'البحث في العملاء', ErrorType.DATABASE);
        return [];
      }
    }, 'Search Customers', 'database', { query });
  }

  // جلب أفضل العملاء (حسب الإنفاق)
  static async getTopCustomers(limit: number = 10): Promise<Customer[]> {
    return measureFunction(async () => {
      try {
        const customers = await allQuery(`
          SELECT c.*, 
                 COUNT(p.id) as totalProjects,
                 COALESCE(SUM(p.totalCost), 0) as totalSpent,
                 COALESCE(SUM(p.paidAmount), 0) as totalPaid,
                 COALESCE(SUM(p.remainingAmount), 0) as totalRemaining
          FROM customers c
          LEFT JOIN projects p ON c.id = p.customerId
          GROUP BY c.id
          HAVING totalSpent > 0
          ORDER BY totalSpent DESC
          LIMIT ?
        `, [limit]);
        
        return ensureArray(customers).map(validateCustomerData);
      } catch (error) {
        handleError(error, 'جلب أفضل العملاء', ErrorType.DATABASE);
        return [];
      }
    }, 'Get Top Customers', 'database', { limit });
  }

  // جلب العملاء المدينين
  static async getCustomersWithDebt(): Promise<Customer[]> {
    return measureFunction(async () => {
      try {
        const customers = await allQuery(`
          SELECT c.*, 
                 COUNT(p.id) as totalProjects,
                 COALESCE(SUM(p.totalCost), 0) as totalSpent,
                 COALESCE(SUM(p.paidAmount), 0) as totalPaid,
                 COALESCE(SUM(p.remainingAmount), 0) as totalRemaining
          FROM customers c
          LEFT JOIN projects p ON c.id = p.customerId
          GROUP BY c.id
          HAVING totalRemaining > 0
          ORDER BY totalRemaining DESC
        `);
        
        return ensureArray(customers).map(validateCustomerData);
      } catch (error) {
        handleError(error, 'جلب العملاء المدينين', ErrorType.DATABASE);
        return [];
      }
    }, 'Get Customers With Debt', 'database');
  }

  // حساب إحصائيات العملاء
  static async getStatistics(): Promise<{
    total: number;
    withProjects: number;
    withDebt: number;
    totalDebt: number;
    averageSpending: number;
  }> {
    return measureFunction(async () => {
      try {
        const customers = await this.getAll();
        
        const stats = {
          total: customers.length,
          withProjects: customers.filter(c => (c as any).totalProjects > 0).length,
          withDebt: customers.filter(c => (c as any).totalRemaining > 0).length,
          totalDebt: safeReduce(customers, (sum, c) => sum + ((c as any).totalRemaining || 0), 0),
          averageSpending: 0
        };
        
        if (stats.withProjects > 0) {
          const totalSpent = safeReduce(customers, (sum, c) => sum + ((c as any).totalSpent || 0), 0);
          stats.averageSpending = totalSpent / stats.withProjects;
        }
        
        return stats;
      } catch (error) {
        handleError(error, 'حساب إحصائيات العملاء', ErrorType.DATABASE);
        return {
          total: 0,
          withProjects: 0,
          withDebt: 0,
          totalDebt: 0,
          averageSpending: 0
        };
      }
    }, 'Get Customer Statistics', 'database');
  }
}
