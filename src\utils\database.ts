// ملف قاعدة البيانات المؤقت
// يوفر واجهة موحدة للتعامل مع قاعدة البيانات عبر Electron API

// التحقق من وجود Electron API
const isElectron = typeof window !== 'undefined' && window.electronAPI;

// دالة تشغيل استعلام
export const runQuery = async (query: string, params: any[] = []): Promise<void> => {
  if (isElectron) {
    try {
      await window.electronAPI.runQuery(query, params);
    } catch (error) {
      console.error('خطأ في تشغيل الاستعلام:', error);
      throw error;
    }
  } else {
    console.warn('Electron API غير متاح');
    throw new Error('قاعدة البيانات غير متاحة');
  }
};

// دالة جلب سجل واحد
export const getQuery = async (query: string, params: any[] = []): Promise<any> => {
  if (isElectron) {
    try {
      return await window.electronAPI.getQuery(query, params);
    } catch (error) {
      console.error('خطأ في جلب السجل:', error);
      throw error;
    }
  } else {
    console.warn('Electron API غير متاح');
    return null;
  }
};

// دالة جلب جميع السجلات
export const allQuery = async (query: string, params: any[] = []): Promise<any[]> => {
  if (isElectron) {
    try {
      const result = await window.electronAPI.allQuery(query, params);
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('خطأ في جلب السجلات:', error);
      throw error;
    }
  } else {
    console.warn('Electron API غير متاح');
    return [];
  }
};

// دالة تشغيل معاملة
export const runTransaction = async (queries: { query: string; params: any[] }[]): Promise<void> => {
  if (isElectron) {
    try {
      // تشغيل الاستعلامات واحداً تلو الآخر
      for (const { query, params } of queries) {
        await runQuery(query, params);
      }
    } catch (error) {
      console.error('خطأ في تشغيل المعاملة:', error);
      throw error;
    }
  } else {
    console.warn('Electron API غير متاح');
    throw new Error('قاعدة البيانات غير متاحة');
  }
};

// دالة إنشاء الجداول
export const createTables = async (): Promise<void> => {
  const tables = [
    // جدول المواد
    `CREATE TABLE IF NOT EXISTS materials (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      pricePerSqm REAL NOT NULL,
      category TEXT,
      availableQuantity REAL DEFAULT 0,
      minQuantity REAL DEFAULT 0,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // جدول العملاء
    `CREATE TABLE IF NOT EXISTS customers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      phone TEXT,
      email TEXT,
      address TEXT,
      notes TEXT,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // جدول المشاريع
    `CREATE TABLE IF NOT EXISTS projects (
      id TEXT PRIMARY KEY,
      customerId TEXT,
      customerName TEXT NOT NULL,
      furnitureType TEXT NOT NULL,
      area REAL NOT NULL,
      materialId TEXT,
      workerId TEXT,
      factoryId TEXT,
      designerId TEXT,
      materialCost REAL DEFAULT 0,
      workerCost REAL DEFAULT 0,
      factoryCost REAL DEFAULT 0,
      designerCost REAL DEFAULT 0,
      totalCost REAL DEFAULT 0,
      paidAmount REAL DEFAULT 0,
      remainingAmount REAL DEFAULT 0,
      status TEXT DEFAULT 'جديد',
      notes TEXT,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customerId) REFERENCES customers(id)
    )`,
    
    // جدول العمال
    `CREATE TABLE IF NOT EXISTS workers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      specialty TEXT NOT NULL,
      pricePerSqm REAL NOT NULL,
      phone TEXT,
      status TEXT DEFAULT 'نشط',
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // جدول المصانع
    `CREATE TABLE IF NOT EXISTS factories (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      specialty TEXT NOT NULL,
      pricePerSqm REAL NOT NULL,
      location TEXT,
      status TEXT DEFAULT 'نشط',
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // جدول المصممين
    `CREATE TABLE IF NOT EXISTS designers (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      specialty TEXT NOT NULL,
      pricePerSqm REAL NOT NULL,
      phone TEXT,
      status TEXT DEFAULT 'نشط',
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // جدول الموظفين
    `CREATE TABLE IF NOT EXISTS employees (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      position TEXT NOT NULL,
      salary REAL NOT NULL,
      phone TEXT,
      email TEXT,
      hireDate TEXT,
      status TEXT DEFAULT 'نشط',
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // جدول المعاملات المالية
    `CREATE TABLE IF NOT EXISTS cash_transactions (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL,
      amount REAL NOT NULL,
      description TEXT,
      projectId TEXT,
      employeeId TEXT,
      date TEXT DEFAULT CURRENT_TIMESTAMP,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id),
      FOREIGN KEY (employeeId) REFERENCES employees(id)
    )`,
    
    // جدول الفواتير
    `CREATE TABLE IF NOT EXISTS invoices (
      id TEXT PRIMARY KEY,
      projectId TEXT NOT NULL,
      customerName TEXT NOT NULL,
      furnitureType TEXT NOT NULL,
      area REAL NOT NULL,
      materialCost REAL DEFAULT 0,
      workerCost REAL DEFAULT 0,
      factoryCost REAL DEFAULT 0,
      designerCost REAL DEFAULT 0,
      totalCost REAL DEFAULT 0,
      status TEXT DEFAULT 'مبدئية',
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
      updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects(id)
    )`
  ];

  try {
    for (const table of tables) {
      await runQuery(table);
    }
    console.log('تم إنشاء جميع الجداول بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء الجداول:', error);
    throw error;
  }
};

// دالة تهيئة قاعدة البيانات
export const initializeDatabase = async (): Promise<void> => {
  try {
    await createTables();
    console.log('تم تهيئة قاعدة البيانات بنجاح');
  } catch (error) {
    console.error('خطأ في تهيئة قاعدة البيانات:', error);
    throw error;
  }
};

// دالة تنظيف قاعدة البيانات
export const cleanupDatabase = async (): Promise<void> => {
  try {
    // حذف السجلات القديمة أو المؤقتة
    await runQuery('DELETE FROM cash_transactions WHERE date < date("now", "-1 year")');
    console.log('تم تنظيف قاعدة البيانات');
  } catch (error) {
    console.error('خطأ في تنظيف قاعدة البيانات:', error);
  }
};

// دالة النسخ الاحتياطي
export const backupDatabase = async (): Promise<string> => {
  if (isElectron && window.electronAPI.backupDatabase) {
    try {
      return await window.electronAPI.backupDatabase();
    } catch (error) {
      console.error('خطأ في النسخ الاحتياطي:', error);
      throw error;
    }
  } else {
    throw new Error('النسخ الاحتياطي غير متاح');
  }
};

// دالة استعادة النسخة الاحتياطية
export const restoreDatabase = async (backupPath: string): Promise<void> => {
  if (isElectron && window.electronAPI.restoreDatabase) {
    try {
      await window.electronAPI.restoreDatabase(backupPath);
    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      throw error;
    }
  } else {
    throw new Error('استعادة النسخة الاحتياطية غير متاحة');
  }
};
