// مكون حوار التأكيد الموحد
import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  AlertCircle,
  Trash2,
  Save,
  RefreshCw
} from 'lucide-react';
import { themeClasses } from '@/styles/theme';
import { cn } from '@/lib/utils';

export type ConfirmationType = 'delete' | 'save' | 'warning' | 'info' | 'success' | 'custom';

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type?: ConfirmationType;
  title?: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  destructive?: boolean;
  icon?: React.ComponentType<{ className?: string }>;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  open,
  onOpenChange,
  type = 'custom',
  title,
  description,
  confirmText,
  cancelText = 'إلغاء',
  onConfirm,
  onCancel,
  loading = false,
  destructive,
  icon: CustomIcon
}) => {
  // تحديد الإعدادات بناءً على النوع
  const getTypeConfig = () => {
    switch (type) {
      case 'delete':
        return {
          title: title || 'تأكيد الحذف',
          confirmText: confirmText || 'حذف',
          icon: Trash2,
          destructive: true,
          confirmClass: 'bg-red-600 hover:bg-red-700 text-white'
        };
      case 'save':
        return {
          title: title || 'تأكيد الحفظ',
          confirmText: confirmText || 'حفظ',
          icon: Save,
          destructive: false,
          confirmClass: 'bg-green-600 hover:bg-green-700 text-white'
        };
      case 'warning':
        return {
          title: title || 'تحذير',
          confirmText: confirmText || 'متابعة',
          icon: AlertTriangle,
          destructive: false,
          confirmClass: 'bg-yellow-600 hover:bg-yellow-700 text-white'
        };
      case 'info':
        return {
          title: title || 'معلومات',
          confirmText: confirmText || 'موافق',
          icon: Info,
          destructive: false,
          confirmClass: 'bg-blue-600 hover:bg-blue-700 text-white'
        };
      case 'success':
        return {
          title: title || 'نجح',
          confirmText: confirmText || 'موافق',
          icon: CheckCircle,
          destructive: false,
          confirmClass: 'bg-green-600 hover:bg-green-700 text-white'
        };
      default:
        return {
          title: title || 'تأكيد',
          confirmText: confirmText || 'موافق',
          icon: CustomIcon || AlertCircle,
          destructive: destructive || false,
          confirmClass: destructive 
            ? 'bg-red-600 hover:bg-red-700 text-white'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        };
    }
  };

  const config = getTypeConfig();
  const Icon = config.icon;

  const handleConfirm = async () => {
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      // الخطأ سيتم التعامل معه في المكون الأب
      console.error('Confirmation action failed:', error);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-3">
            <div className={cn(
              'p-2 rounded-full',
              config.destructive ? 'bg-red-100' : 'bg-blue-100'
            )}>
              <Icon className={cn(
                'h-5 w-5',
                config.destructive ? 'text-red-600' : 'text-blue-600'
              )} />
            </div>
            {config.title}
          </AlertDialogTitle>
          <AlertDialogDescription className={cn(
            themeClasses.bodyText,
            'text-right leading-relaxed'
          )}>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="flex gap-2 justify-end">
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={loading}
            className="min-w-20"
          >
            {cancelText}
          </AlertDialogCancel>
          
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading}
            className={cn(config.confirmClass, 'min-w-20')}
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin" />
                جاري المعالجة...
              </div>
            ) : (
              config.confirmText
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

// Hook لاستخدام حوارات التأكيد
export const useConfirmation = () => {
  const [dialogState, setDialogState] = React.useState<{
    open: boolean;
    type: ConfirmationType;
    title?: string;
    description: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void | Promise<void>;
    onCancel?: () => void;
    destructive?: boolean;
    icon?: React.ComponentType<{ className?: string }>;
  }>({
    open: false,
    type: 'custom',
    description: '',
    onConfirm: () => {}
  });

  const [loading, setLoading] = React.useState(false);

  const showConfirmation = (options: {
    type?: ConfirmationType;
    title?: string;
    description: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void | Promise<void>;
    onCancel?: () => void;
    destructive?: boolean;
    icon?: React.ComponentType<{ className?: string }>;
  }) => {
    setDialogState({
      open: true,
      type: options.type || 'custom',
      title: options.title,
      description: options.description,
      confirmText: options.confirmText,
      cancelText: options.cancelText,
      onConfirm: options.onConfirm,
      onCancel: options.onCancel,
      destructive: options.destructive,
      icon: options.icon
    });
  };

  const handleConfirm = async () => {
    setLoading(true);
    try {
      await dialogState.onConfirm();
      setDialogState(prev => ({ ...prev, open: false }));
    } catch (error) {
      // يمكن إضافة معالجة الأخطاء هنا
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (dialogState.onCancel) {
      dialogState.onCancel();
    }
    setDialogState(prev => ({ ...prev, open: false }));
  };

  const confirmDelete = (description: string, onConfirm: () => void | Promise<void>) => {
    showConfirmation({
      type: 'delete',
      description,
      onConfirm
    });
  };

  const confirmSave = (description: string, onConfirm: () => void | Promise<void>) => {
    showConfirmation({
      type: 'save',
      description,
      onConfirm
    });
  };

  const showWarning = (description: string, onConfirm: () => void | Promise<void>) => {
    showConfirmation({
      type: 'warning',
      description,
      onConfirm
    });
  };

  const showInfo = (description: string, onConfirm: () => void | Promise<void>) => {
    showConfirmation({
      type: 'info',
      description,
      onConfirm
    });
  };

  const ConfirmationComponent = () => (
    <ConfirmationDialog
      open={dialogState.open}
      onOpenChange={(open) => setDialogState(prev => ({ ...prev, open }))}
      type={dialogState.type}
      title={dialogState.title}
      description={dialogState.description}
      confirmText={dialogState.confirmText}
      cancelText={dialogState.cancelText}
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      loading={loading}
      destructive={dialogState.destructive}
      icon={dialogState.icon}
    />
  );

  return {
    showConfirmation,
    confirmDelete,
    confirmSave,
    showWarning,
    showInfo,
    ConfirmationComponent
  };
};

export default ConfirmationDialog;
