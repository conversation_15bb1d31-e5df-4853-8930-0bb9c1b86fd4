// خدمة مراقبة الأداء
// تهدف لمراقبة وتحسين أداء التطبيق

import { APP_CONFIG } from '@/config/appConfig';

// أنواع قياسات الأداء
export interface PerformanceMetric {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  category: 'database' | 'ui' | 'network' | 'calculation' | 'other';
  metadata?: Record<string, any>;
}

// مخزن قياسات الأداء
const performanceMetrics: PerformanceMetric[] = [];
const activeMetrics: Map<string, PerformanceMetric> = new Map();

// إنشاء معرف فريد للقياس
const generateMetricId = (): string => {
  return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// بدء قياس الأداء
export const startPerformanceMetric = (
  name: string,
  category: PerformanceMetric['category'] = 'other',
  metadata?: Record<string, any>
): string => {
  const id = generateMetricId();
  const metric: PerformanceMetric = {
    id,
    name,
    startTime: performance.now(),
    category,
    metadata
  };
  
  activeMetrics.set(id, metric);
  return id;
};

// إنهاء قياس الأداء
export const endPerformanceMetric = (id: string): PerformanceMetric | null => {
  const metric = activeMetrics.get(id);
  if (!metric) {
    console.warn(`لم يتم العثور على قياس الأداء: ${id}`);
    return null;
  }
  
  metric.endTime = performance.now();
  metric.duration = metric.endTime - metric.startTime;
  
  activeMetrics.delete(id);
  performanceMetrics.push(metric);
  
  // الحفاظ على حد أقصى لعدد القياسات
  if (performanceMetrics.length > APP_CONFIG.performance.maxCacheSize * 10) {
    performanceMetrics.splice(0, performanceMetrics.length - APP_CONFIG.performance.maxCacheSize * 10);
  }
  
  // تسجيل القياسات البطيئة
  if (metric.duration > 1000) { // أكثر من ثانية
    console.warn(`عملية بطيئة: ${metric.name} - ${metric.duration.toFixed(2)}ms`);
  }
  
  return metric;
};

// قياس دالة تلقائياً
export const measureFunction = async <T>(
  fn: () => Promise<T> | T,
  name: string,
  category: PerformanceMetric['category'] = 'other',
  metadata?: Record<string, any>
): Promise<T> => {
  const id = startPerformanceMetric(name, category, metadata);
  
  try {
    const result = await fn();
    endPerformanceMetric(id);
    return result;
  } catch (error) {
    endPerformanceMetric(id);
    throw error;
  }
};

// قياس مكون React
export const measureComponent = (componentName: string) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const id = startPerformanceMetric(
        `${componentName}.${propertyName}`,
        'ui',
        { component: componentName, method: propertyName }
      );
      
      try {
        const result = await originalMethod.apply(this, args);
        endPerformanceMetric(id);
        return result;
      } catch (error) {
        endPerformanceMetric(id);
        throw error;
      }
    };
    
    return descriptor;
  };
};

// الحصول على إحصائيات الأداء
export const getPerformanceStats = () => {
  const stats = {
    totalMetrics: performanceMetrics.length,
    activeMetrics: activeMetrics.size,
    averageDuration: 0,
    slowestOperations: [] as PerformanceMetric[],
    byCategory: {} as Record<string, { count: number; avgDuration: number }>,
    recentMetrics: [] as PerformanceMetric[]
  };
  
  if (performanceMetrics.length === 0) {
    return stats;
  }
  
  // حساب المتوسط
  const totalDuration = performanceMetrics.reduce((sum, metric) => 
    sum + (metric.duration || 0), 0
  );
  stats.averageDuration = totalDuration / performanceMetrics.length;
  
  // أبطأ العمليات
  stats.slowestOperations = [...performanceMetrics]
    .sort((a, b) => (b.duration || 0) - (a.duration || 0))
    .slice(0, 10);
  
  // إحصائيات حسب الفئة
  performanceMetrics.forEach(metric => {
    if (!stats.byCategory[metric.category]) {
      stats.byCategory[metric.category] = { count: 0, avgDuration: 0 };
    }
    stats.byCategory[metric.category].count++;
  });
  
  // حساب المتوسط لكل فئة
  Object.keys(stats.byCategory).forEach(category => {
    const categoryMetrics = performanceMetrics.filter(m => m.category === category);
    const categoryDuration = categoryMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
    stats.byCategory[category].avgDuration = categoryDuration / categoryMetrics.length;
  });
  
  // القياسات الحديثة (آخر 10)
  stats.recentMetrics = performanceMetrics.slice(-10);
  
  return stats;
};

// مراقبة استخدام الذاكرة
export const getMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    };
  }
  return null;
};

// مراقبة FPS (إطارات في الثانية)
let fpsCounter = 0;
let lastFpsTime = performance.now();
let currentFps = 0;

const updateFps = () => {
  fpsCounter++;
  const now = performance.now();
  
  if (now - lastFpsTime >= 1000) {
    currentFps = Math.round((fpsCounter * 1000) / (now - lastFpsTime));
    fpsCounter = 0;
    lastFpsTime = now;
  }
  
  requestAnimationFrame(updateFps);
};

// بدء مراقبة FPS
export const startFpsMonitoring = () => {
  requestAnimationFrame(updateFps);
};

// الحصول على FPS الحالي
export const getCurrentFps = () => currentFps;

// تحليل أداء قاعدة البيانات
export const analyzeDatabasePerformance = () => {
  const dbMetrics = performanceMetrics.filter(m => m.category === 'database');
  
  if (dbMetrics.length === 0) {
    return null;
  }
  
  const analysis = {
    totalQueries: dbMetrics.length,
    averageQueryTime: 0,
    slowQueries: [] as PerformanceMetric[],
    queryTypes: {} as Record<string, number>
  };
  
  // حساب متوسط وقت الاستعلام
  const totalTime = dbMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
  analysis.averageQueryTime = totalTime / dbMetrics.length;
  
  // الاستعلامات البطيئة (أكثر من 100ms)
  analysis.slowQueries = dbMetrics
    .filter(m => (m.duration || 0) > 100)
    .sort((a, b) => (b.duration || 0) - (a.duration || 0));
  
  // أنواع الاستعلامات
  dbMetrics.forEach(metric => {
    const queryType = metric.metadata?.queryType || 'unknown';
    analysis.queryTypes[queryType] = (analysis.queryTypes[queryType] || 0) + 1;
  });
  
  return analysis;
};

// تحسينات الأداء التلقائية
export const applyPerformanceOptimizations = () => {
  const stats = getPerformanceStats();
  const suggestions: string[] = [];
  
  // اقتراحات بناءً على الإحصائيات
  if (stats.averageDuration > 500) {
    suggestions.push('متوسط وقت العمليات مرتفع - فكر في تحسين الخوارزميات');
  }
  
  if (stats.byCategory.database?.avgDuration > 200) {
    suggestions.push('استعلامات قاعدة البيانات بطيئة - فكر في إضافة فهارس');
  }
  
  if (stats.byCategory.ui?.avgDuration > 100) {
    suggestions.push('عمليات واجهة المستخدم بطيئة - فكر في استخدام React.memo');
  }
  
  const memoryUsage = getMemoryUsage();
  if (memoryUsage && memoryUsage.usagePercentage > 80) {
    suggestions.push('استخدام الذاكرة مرتفع - فكر في تحسين إدارة الذاكرة');
  }
  
  if (currentFps < 30) {
    suggestions.push('معدل الإطارات منخفض - فكر في تحسين الرسوميات');
  }
  
  return {
    stats,
    memoryUsage,
    fps: currentFps,
    suggestions
  };
};

// تصدير تقرير الأداء
export const exportPerformanceReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    stats: getPerformanceStats(),
    memoryUsage: getMemoryUsage(),
    fps: currentFps,
    databaseAnalysis: analyzeDatabasePerformance(),
    optimizations: applyPerformanceOptimizations(),
    metrics: performanceMetrics.slice(-100) // آخر 100 قياس
  };
  
  return JSON.stringify(report, null, 2);
};

// مسح بيانات الأداء
export const clearPerformanceData = () => {
  performanceMetrics.length = 0;
  activeMetrics.clear();
  console.log('تم مسح بيانات الأداء');
};

// Hook لمراقبة الأداء في React
export const usePerformanceMonitor = (componentName: string) => {
  const measureRender = () => {
    return measureFunction(
      () => Promise.resolve(),
      `${componentName} render`,
      'ui',
      { component: componentName }
    );
  };
  
  const measureEffect = (effectName: string) => {
    return (fn: () => void | Promise<void>) => {
      return measureFunction(
        fn,
        `${componentName} ${effectName}`,
        'ui',
        { component: componentName, effect: effectName }
      );
    };
  };
  
  return {
    measureRender,
    measureEffect,
    getStats: getPerformanceStats
  };
};

// تهيئة مراقب الأداء
export const initializePerformanceMonitor = () => {
  startFpsMonitoring();
  
  // مراقبة دورية للأداء
  setInterval(() => {
    const optimizations = applyPerformanceOptimizations();
    if (optimizations.suggestions.length > 0) {
      console.group('🔍 اقتراحات تحسين الأداء:');
      optimizations.suggestions.forEach(suggestion => {
        console.log(`💡 ${suggestion}`);
      });
      console.groupEnd();
    }
  }, 60000); // كل دقيقة
  
  console.log('تم تهيئة مراقب الأداء');
};
