// مكون معالجة الأخطاء في React
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
// import { handleReactError, getErrorMessage, ErrorType } from '@/services/errorHandler';
import { getAppName } from '@/config/appConfig';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // تسجيل الخطأ في الكونسول
    console.error('خطأ في React Component:', error, errorInfo);

    // حفظ معلومات الخطأ في الحالة
    this.setState({
      error,
      errorInfo
    });

    // استدعاء callback إضافي إذا تم توفيره
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.hash = '#/';
    this.setState({ hasError: false });
  };

  handleRetry = () => {
    this.setState({ hasError: false });
  };

  render() {
    if (this.state.hasError) {
      // إذا تم توفير fallback مخصص، استخدمه
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // واجهة الخطأ الافتراضية
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <AlertTriangle className="h-16 w-16 text-red-500" />
              </div>
              <CardTitle className="text-2xl text-red-600">
                حدث خطأ غير متوقع
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <div className="text-center text-gray-600">
                <p className="mb-2">
                  نعتذر، حدث خطأ في {getAppName()}
                </p>
                <p className="text-sm">
                  معرف الخطأ: {this.state.errorId}
                </p>
              </div>

              {/* تفاصيل الخطأ للمطورين */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="bg-gray-100 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center">
                    <Bug className="h-4 w-4 mr-2" />
                    تفاصيل الخطأ (وضع التطوير)
                  </h4>
                  <div className="text-sm font-mono bg-red-50 p-3 rounded border-l-4 border-red-400">
                    <p className="font-semibold text-red-700">
                      {this.state.error.name}: {this.state.error.message}
                    </p>
                    {this.state.error.stack && (
                      <pre className="mt-2 text-xs text-red-600 whitespace-pre-wrap">
                        {this.state.error.stack}
                      </pre>
                    )}
                  </div>
                  
                  {this.state.errorInfo?.componentStack && (
                    <div className="mt-3">
                      <p className="font-semibold text-gray-700 mb-1">
                        مكدس المكونات:
                      </p>
                      <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded whitespace-pre-wrap">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              )}

              {/* أزرار الإجراءات */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button 
                  onClick={this.handleRetry}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  إعادة المحاولة
                </Button>
                
                <Button 
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  العودة للرئيسية
                </Button>
                
                <Button 
                  onClick={this.handleReload}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  إعادة تحميل الصفحة
                </Button>
              </div>

              {/* نصائح للمستخدم */}
              <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                <h4 className="font-semibold text-blue-800 mb-2">
                  ماذا يمكنك فعله؟
                </h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• جرب إعادة تحميل الصفحة</li>
                  <li>• تأكد من اتصالك بالإنترنت</li>
                  <li>• امسح ذاكرة التخزين المؤقت للمتصفح</li>
                  <li>• إذا استمر الخطأ، تواصل مع الدعم الفني</li>
                </ul>
              </div>

              {/* معلومات إضافية */}
              <div className="text-center text-xs text-gray-500">
                <p>
                  وقت الخطأ: {new Date().toLocaleString('ar-SA')}
                </p>
                <p>
                  المتصفح: {navigator.userAgent.split(' ')[0]}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// مكون مبسط لمعالجة الأخطاء
export const SimpleErrorBoundary: React.FC<{
  children: ReactNode;
  message?: string;
}> = ({ children, message = "حدث خطأ في هذا القسم" }) => {
  return (
    <ErrorBoundary
      fallback={
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-700">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">{message}</span>
          </div>
          <Button 
            onClick={() => window.location.reload()} 
            size="sm" 
            variant="outline" 
            className="mt-2"
          >
            إعادة المحاولة
          </Button>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
};

// Hook لاستخدام معالجة الأخطاء في المكونات الوظيفية
export const useErrorHandler = () => {
  const handleError = (error: Error, context?: string) => {
    console.error(`خطأ في ${context || 'مكون غير محدد'}:`, error);
  };

  const handleAsyncError = async (
    asyncFn: () => Promise<any>,
    context?: string
  ) => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error as Error, context);
      throw error;
    }
  };

  return {
    handleError,
    handleAsyncError
  };
};

export default ErrorBoundary;
