import { runQuery, getQuery, allQuery } from '../utils/database';
import {
  Material,
  Worker,
  Factory,
  Designer,
  Customer,
  Project,
  Invoice,
  Employee,
  CashTransaction
} from '../utils/dataManager';
import { measureFunction } from './performanceMonitor';
import { handleError, ErrorType } from './errorHandler';
import {
  ensureArray,
  validateId,
  validateMaterialData,
  validateProjectData,
  validateWorkerData
} from '@/utils/dataValidation';

// خدمات المواد المحسنة
export class MaterialService {
  static async getAll(): Promise<Material[]> {
    return measureFunction(async () => {
      try {
        const materials = await allQuery('SELECT * FROM materials ORDER BY name');
        return ensureArray(materials).map(validateMaterialData);
      } catch (error) {
        handleError(error, 'جلب جميع المواد', ErrorType.DATABASE);
        return [];
      }
    }, 'Get All Materials', 'database');
  }

  static async getById(id: string): Promise<Material | null> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) return null;

        const material = await getQuery('SELECT * FROM materials WHERE id = ?', [validId]);
        return material ? validateMaterialData(material) : null;
      } catch (error) {
        handleError(error, 'جلب مادة بالمعرف', ErrorType.DATABASE);
        return null;
      }
    }, 'Get Material By ID', 'database', { id });
  }

  static async create(material: Omit<Material, 'id'>): Promise<string> {
    return measureFunction(async () => {
      try {
        const validatedMaterial = validateMaterialData(material);
        const id = Date.now().toString();

        await runQuery(
          'INSERT INTO materials (id, name, description, pricePerSqm, category, availableQuantity, minQuantity) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [
            id,
            validatedMaterial.name,
            validatedMaterial.description,
            validatedMaterial.pricePerSqm,
            validatedMaterial.category,
            validatedMaterial.availableQuantity || 0,
            validatedMaterial.minQuantity || 0
          ]
        );
        return id;
      } catch (error) {
        handleError(error, 'إنشاء مادة جديدة', ErrorType.DATABASE);
        throw error;
      }
    }, 'Create Material', 'database');
  }

  static async update(id: string, material: Partial<Material>): Promise<void> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) throw new Error('معرف المادة غير صحيح');

        const validatedMaterial = validateMaterialData(material);
        const fields = Object.keys(validatedMaterial).filter(key => key !== 'id' && validatedMaterial[key as keyof Material] !== undefined);

        if (fields.length === 0) return;

        const values = fields.map(key => validatedMaterial[key as keyof Material]);
        const setClause = fields.map(field => `${field} = ?`).join(', ');

        await runQuery(
          `UPDATE materials SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
          [...values, validId]
        );
      } catch (error) {
        handleError(error, 'تحديث المادة', ErrorType.DATABASE);
        throw error;
      }
    }, 'Update Material', 'database', { id });
  }

  static async delete(id: string): Promise<void> {
    return measureFunction(async () => {
      try {
        const validId = validateId(id);
        if (!validId) throw new Error('معرف المادة غير صحيح');

        await runQuery('DELETE FROM materials WHERE id = ?', [validId]);
      } catch (error) {
        handleError(error, 'حذف المادة', ErrorType.DATABASE);
        throw error;
      }
    }, 'Delete Material', 'database', { id });
  }

  static async getLowStock(): Promise<Material[]> {
    return measureFunction(async () => {
      try {
        const materials = await allQuery(
          'SELECT * FROM materials WHERE availableQuantity <= minQuantity ORDER BY name'
        );
        return ensureArray(materials).map(validateMaterialData);
      } catch (error) {
        handleError(error, 'جلب المواد منخفضة المخزون', ErrorType.DATABASE);
        return [];
      }
    }, 'Get Low Stock Materials', 'database');
  }
}

// خدمات العمال
export class WorkerService {
  static async getAll(): Promise<Worker[]> {
    return await allQuery('SELECT * FROM workers ORDER BY name');
  }

  static async getById(id: string): Promise<Worker | null> {
    return await getQuery('SELECT * FROM workers WHERE id = ?', [id]);
  }

  static async create(worker: Omit<Worker, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO workers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
      [id, worker.name, worker.specialty, worker.pricePerSqm, worker.phone]
    );
    return id;
  }

  static async update(id: string, worker: Partial<Worker>): Promise<void> {
    const fields = Object.keys(worker).filter(key => key !== 'id');
    const values = fields.map(key => worker[key as keyof Worker]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    await runQuery(
      `UPDATE workers SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM workers WHERE id = ?', [id]);
  }
}

// خدمات المصانع
export class FactoryService {
  static async getAll(): Promise<Factory[]> {
    return await allQuery('SELECT * FROM factories ORDER BY name');
  }

  static async getById(id: string): Promise<Factory | null> {
    return await getQuery('SELECT * FROM factories WHERE id = ?', [id]);
  }

  static async create(factory: Omit<Factory, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO factories (id, name, specialty, pricePerSqm, location) VALUES (?, ?, ?, ?, ?)',
      [id, factory.name, factory.specialty, factory.pricePerSqm, factory.location]
    );
    return id;
  }

  static async update(id: string, factory: Partial<Factory>): Promise<void> {
    const fields = Object.keys(factory).filter(key => key !== 'id');
    const values = fields.map(key => factory[key as keyof Factory]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    await runQuery(
      `UPDATE factories SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM factories WHERE id = ?', [id]);
  }
}

// خدمات المصممين
export class DesignerService {
  static async getAll(): Promise<Designer[]> {
    return await allQuery('SELECT * FROM designers ORDER BY name');
  }

  static async getById(id: string): Promise<Designer | null> {
    return await getQuery('SELECT * FROM designers WHERE id = ?', [id]);
  }

  static async create(designer: Omit<Designer, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO designers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
      [id, designer.name, designer.specialty, designer.pricePerSqm, designer.phone]
    );
    return id;
  }

  static async update(id: string, designer: Partial<Designer>): Promise<void> {
    const fields = Object.keys(designer).filter(key => key !== 'id');
    const values = fields.map(key => designer[key as keyof Designer]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    await runQuery(
      `UPDATE designers SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM designers WHERE id = ?', [id]);
  }
}

// خدمات العملاء
export class CustomerService {
  static async getAll(): Promise<Customer[]> {
    return await allQuery('SELECT * FROM customers ORDER BY name');
  }

  static async getById(id: string): Promise<Customer | null> {
    return await getQuery('SELECT * FROM customers WHERE id = ?', [id]);
  }

  static async create(customer: Omit<Customer, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO customers (id, name, phone, email, address, totalProjects, totalSpent) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [id, customer.name, customer.phone, customer.email, customer.address, customer.totalProjects, customer.totalSpent]
    );
    return id;
  }

  static async update(id: string, customer: Partial<Customer>): Promise<void> {
    const fields = Object.keys(customer).filter(key => key !== 'id');
    const values = fields.map(key => customer[key as keyof Customer]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    await runQuery(
      `UPDATE customers SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM customers WHERE id = ?', [id]);
  }

  static async updateStats(name: string, phone?: string, projectCost?: number): Promise<void> {
    const existing = await getQuery('SELECT * FROM customers WHERE name = ?', [name]);

    if (existing) {
      await runQuery(
        'UPDATE customers SET totalProjects = totalProjects + 1, totalSpent = totalSpent + ?, phone = COALESCE(?, phone), updatedAt = CURRENT_TIMESTAMP WHERE name = ?',
        [projectCost || 0, phone, name]
      );
    } else {
      await this.create({
        name,
        phone,
        totalProjects: 1,
        totalSpent: projectCost || 0,
        createdAt: new Date().toISOString()
      });
    }
  }
}

// خدمات المشاريع
export class ProjectService {
  static async getAll(): Promise<Project[]> {
    const projects = await allQuery(`
      SELECT p.*,
             m.name as materialName, m.pricePerSqm as materialPrice,
             w.name as workerName, w.pricePerSqm as workerPrice,
             f.name as factoryName, f.pricePerSqm as factoryPrice,
             d.name as designerName, d.pricePerSqm as designerPrice
      FROM projects p
      LEFT JOIN materials m ON p.materialId = m.id
      LEFT JOIN workers w ON p.workerId = w.id
      LEFT JOIN factories f ON p.factoryId = f.id
      LEFT JOIN designers d ON p.designerId = d.id
      ORDER BY p.createdAt DESC
    `);

    return projects.map(p => ({
      id: p.id,
      customerName: p.customerName,
      customerPhone: p.customerPhone,
      area: p.area,
      furnitureType: p.furnitureType,
      selectedMaterial: { id: p.materialId, name: p.materialName, pricePerSqm: p.materialPrice },
      selectedWorker: { id: p.workerId, name: p.workerName, pricePerSqm: p.workerPrice },
      selectedFactory: { id: p.factoryId, name: p.factoryName, pricePerSqm: p.factoryPrice },
      selectedDesigner: { id: p.designerId, name: p.designerName, pricePerSqm: p.designerPrice },
      totalCost: p.totalCost,
      breakdown: {
        materialCost: p.materialCost,
        workerCost: p.workerCost,
        factoryCost: p.factoryCost,
        designerCost: p.designerCost
      },
      paidAmount: p.paidAmount,
      remainingAmount: p.remainingAmount,
      status: p.status,
      invoiceStatus: p.invoiceStatus,
      createdAt: p.createdAt,
      completedAt: p.completedAt,
      notes: p.notes
    }));
  }

  static async getById(id: string): Promise<Project | null> {
    const project = await getQuery(`
      SELECT p.*,
             m.name as materialName, m.pricePerSqm as materialPrice,
             w.name as workerName, w.pricePerSqm as workerPrice,
             f.name as factoryName, f.pricePerSqm as factoryPrice,
             d.name as designerName, d.pricePerSqm as designerPrice
      FROM projects p
      LEFT JOIN materials m ON p.materialId = m.id
      LEFT JOIN workers w ON p.workerId = w.id
      LEFT JOIN factories f ON p.factoryId = f.id
      LEFT JOIN designers d ON p.designerId = d.id
      WHERE p.id = ?
    `, [id]);

    if (!project) return null;

    return {
      id: project.id,
      customerName: project.customerName,
      customerPhone: project.customerPhone,
      area: project.area,
      furnitureType: project.furnitureType,
      selectedMaterial: { id: project.materialId, name: project.materialName, pricePerSqm: project.materialPrice },
      selectedWorker: { id: project.workerId, name: project.workerName, pricePerSqm: project.workerPrice },
      selectedFactory: { id: project.factoryId, name: project.factoryName, pricePerSqm: project.factoryPrice },
      selectedDesigner: { id: project.designerId, name: project.designerName, pricePerSqm: project.designerPrice },
      totalCost: project.totalCost,
      breakdown: {
        materialCost: project.materialCost,
        workerCost: project.workerCost,
        factoryCost: project.factoryCost,
        designerCost: project.designerCost
      },
      paidAmount: project.paidAmount,
      remainingAmount: project.remainingAmount,
      status: project.status,
      invoiceStatus: project.invoiceStatus,
      createdAt: project.createdAt,
      completedAt: project.completedAt,
      notes: project.notes
    };
  }

  static async create(project: Omit<Project, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(`
      INSERT INTO projects (
        id, customerName, customerPhone, area, furnitureType,
        materialId, workerId, factoryId, designerId,
        totalCost, materialCost, workerCost, factoryCost, designerCost,
        paidAmount, remainingAmount, status, invoiceStatus, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id, project.customerName, project.customerPhone, project.area, project.furnitureType,
      project.selectedMaterial.id, project.selectedWorker.id, project.selectedFactory.id, project.selectedDesigner.id,
      project.totalCost, project.breakdown.materialCost, project.breakdown.workerCost,
      project.breakdown.factoryCost, project.breakdown.designerCost,
      project.paidAmount, project.remainingAmount, project.status, project.invoiceStatus, project.notes
    ]);

    // إنشاء فاتورة مبدئية
    await InvoiceService.create({
      projectId: id,
      customerName: project.customerName,
      customerPhone: project.customerPhone,
      totalAmount: project.totalCost,
      status: 'مبدئية',
      type: 'مبدئية',
      createdAt: new Date().toISOString(),
      notes: `فاتورة مبدئية لمشروع ${project.furnitureType}`
    });

    // تحديث بيانات العميل
    await CustomerService.updateStats(project.customerName, project.customerPhone, project.totalCost);

    return id;
  }

  static async update(id: string, project: Partial<Project>): Promise<void> {
    const fields = Object.keys(project).filter(key => key !== 'id' && key !== 'selectedMaterial' && key !== 'selectedWorker' && key !== 'selectedFactory' && key !== 'selectedDesigner' && key !== 'breakdown');
    const values = fields.map(key => project[key as keyof Project]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    if (setClause) {
      await runQuery(
        `UPDATE projects SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
        [...values, id]
      );
    }
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM projects WHERE id = ?', [id]);
  }
}

// خدمات الفواتير
export class InvoiceService {
  static async getAll(): Promise<Invoice[]> {
    return await allQuery('SELECT * FROM invoices ORDER BY createdAt DESC');
  }

  static async getById(id: string): Promise<Invoice | null> {
    return await getQuery('SELECT * FROM invoices WHERE id = ?', [id]);
  }

  static async create(invoice: Omit<Invoice, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO invoices (id, projectId, customerName, customerPhone, totalAmount, status, type, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [id, invoice.projectId, invoice.customerName, invoice.customerPhone, invoice.totalAmount, invoice.status, invoice.type, invoice.notes]
    );
    return id;
  }

  static async update(id: string, invoice: Partial<Invoice>): Promise<void> {
    const fields = Object.keys(invoice).filter(key => key !== 'id');
    const values = fields.map(key => invoice[key as keyof Invoice]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    await runQuery(
      `UPDATE invoices SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM invoices WHERE id = ?', [id]);
  }

  static async convertToManufacturing(invoiceId: string): Promise<void> {
    await runQuery(
      'UPDATE invoices SET status = ?, type = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
      ['تصنيع', 'تصنيع', invoiceId]
    );

    // تحديث حالة المشروع
    const invoice = await this.getById(invoiceId);
    if (invoice) {
      await runQuery(
        'UPDATE projects SET invoiceStatus = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?',
        ['تصنيع', invoice.projectId]
      );
    }
  }
}

// خدمات الموظفين
export class EmployeeService {
  static async getAll(): Promise<Employee[]> {
    return await allQuery('SELECT * FROM employees ORDER BY name');
  }

  static async getById(id: string): Promise<Employee | null> {
    return await getQuery('SELECT * FROM employees WHERE id = ?', [id]);
  }

  static async create(employee: Omit<Employee, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO employees (id, name, position, baseSalary, bonuses, deductions, totalSalary, phone, hireDate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [id, employee.name, employee.position, employee.baseSalary, employee.bonuses, employee.deductions, employee.totalSalary, employee.phone, employee.hireDate]
    );
    return id;
  }

  static async update(id: string, employee: Partial<Employee>): Promise<void> {
    const fields = Object.keys(employee).filter(key => key !== 'id');
    const values = fields.map(key => employee[key as keyof Employee]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    await runQuery(
      `UPDATE employees SET ${setClause}, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [...values, id]
    );
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM employees WHERE id = ?', [id]);
  }

  static async paySalary(employeeId: string, amount: number, month: string): Promise<void> {
    // إضافة معاملة مصروف للمرتب
    await CashTransactionService.create({
      type: 'مصروف',
      category: 'مرتب',
      amount,
      description: `مرتب ${month}`,
      employeeId,
      date: new Date().toISOString().split('T')[0]
    });
  }
}

// خدمات معاملات الخزينة
export class CashTransactionService {
  static async getAll(): Promise<CashTransaction[]> {
    return await allQuery('SELECT * FROM cash_transactions ORDER BY date DESC, createdAt DESC');
  }

  static async getById(id: string): Promise<CashTransaction | null> {
    return await getQuery('SELECT * FROM cash_transactions WHERE id = ?', [id]);
  }

  static async create(transaction: Omit<CashTransaction, 'id'>): Promise<string> {
    const id = Date.now().toString();
    await runQuery(
      'INSERT INTO cash_transactions (id, type, category, amount, description, projectId, employeeId, date) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [id, transaction.type, transaction.category, transaction.amount, transaction.description, transaction.projectId, transaction.employeeId, transaction.date]
    );
    return id;
  }

  static async update(id: string, transaction: Partial<CashTransaction>): Promise<void> {
    const fields = Object.keys(transaction).filter(key => key !== 'id');
    const values = fields.map(key => transaction[key as keyof CashTransaction]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    await runQuery(
      `UPDATE cash_transactions SET ${setClause} WHERE id = ?`,
      [...values, id]
    );
  }

  static async delete(id: string): Promise<void> {
    await runQuery('DELETE FROM cash_transactions WHERE id = ?', [id]);
  }

  static async getSummary(): Promise<any> {
    const transactions = await this.getAll();
    const currentMonth = new Date().toISOString().slice(0, 7);

    const totalIncome = transactions
      .filter(t => t.type === 'دخل')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = transactions
      .filter(t => t.type === 'مصروف')
      .reduce((sum, t) => sum + t.amount, 0);

    const monthlyIncome = transactions
      .filter(t => t.type === 'دخل' && t.date.startsWith(currentMonth))
      .reduce((sum, t) => sum + t.amount, 0);

    const monthlyExpenses = transactions
      .filter(t => t.type === 'مصروف' && t.date.startsWith(currentMonth))
      .reduce((sum, t) => sum + t.amount, 0);

    // التأكد من أن transactions مصفوفة صحيحة
    const safeTransactions = Array.isArray(transactions) ? transactions : [];

    const projectPayments = safeTransactions
      .filter(t => t.type === 'دخل' && t.category === 'مشروع')
      .reduce((sum, t) => {
        const amount = t.amount || 0;
        return sum + amount;
      }, 0);

    const salaryPayments = safeTransactions
      .filter(t => t.type === 'مصروف' && t.category === 'مرتب')
      .reduce((sum, t) => {
        const amount = t.amount || 0;
        return sum + amount;
      }, 0);

    const generalExpenses = safeTransactions
      .filter(t => t.type === 'مصروف' && t.category !== 'مرتب')
      .reduce((sum, t) => {
        const amount = t.amount || 0;
        return sum + amount;
      }, 0);

    return {
      totalIncome,
      totalExpenses,
      currentBalance: totalIncome - totalExpenses,
      monthlyIncome,
      monthlyExpenses,
      projectPayments,
      salaryPayments,
      generalExpenses
    };
  }
}
// تسجيل التكاليف التلقائي - تم إزالة هذا الكود المكرر
// يتم التعامل مع التكاليف من خلال خدمات أخرى مخصصة
