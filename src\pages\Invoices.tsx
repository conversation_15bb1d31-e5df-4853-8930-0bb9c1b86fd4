
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { <PERSON><PERSON><PERSON><PERSON>, Edit3, Trash2, <PERSON>Circle, ArrowLeft, DollarSign, RefreshCw, AlertTriangle } from "lucide-react";
import { formatLibyanDinar } from "@/utils/calculations";
import { useToast } from "@/hooks/use-toast";
import { 
  getInvoices, 
  saveInvoices, 
  convertInvoiceToManufacturing, 
  addCashTransaction,
  getProjects,
  saveProjects,
  Invoice,
  CashTransaction 
} from "@/utils/dataManager";
import Navbar from "@/components/Navbar";

const Invoices = () => {
  const { toast } = useToast();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState("");
  const [paymentNotes, setPaymentNotes] = useState("");

  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = () => {
    setInvoices(getInvoices());
  };

  const handleConvertToManufacturing = (invoiceId: string) => {
    convertInvoiceToManufacturing(invoiceId);
    loadInvoices();
    toast({
      title: "تم تحويل الفاتورة",
      description: "تم تحويل الفاتورة إلى فاتورة تصنيع وتسجيل الإيراد في الخزينة",
    });
  };

  const handleEditInvoice = (invoice: Invoice) => {
    setEditingInvoice({ ...invoice });
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    if (!editingInvoice) return;

    const allInvoices = getInvoices();
    const index = allInvoices.findIndex(inv => inv.id === editingInvoice.id);
    if (index !== -1) {
      allInvoices[index] = { ...editingInvoice, updatedAt: new Date().toISOString() };
      saveInvoices(allInvoices);
      loadInvoices();
      setIsEditDialogOpen(false);
      setEditingInvoice(null);
      
      toast({
        title: "تم تحديث الفاتورة",
        description: "تم حفظ التعديلات بنجاح",
      });
    }
  };

  const handleRevertToInitial = (invoice: Invoice) => {
    const allInvoices = getInvoices();
    const index = allInvoices.findIndex(inv => inv.id === invoice.id);
    if (index !== -1) {
      allInvoices[index] = { 
        ...invoice, 
        status: 'مبدئية', 
        type: 'مبدئية', 
        updatedAt: new Date().toISOString() 
      };
      saveInvoices(allInvoices);
      
      // إلغاء معاملة الإيراد من الخزينة
      const transaction: CashTransaction = {
        id: Date.now().toString(),
        type: 'مصروف',
        category: 'أخرى',
        amount: invoice.totalAmount,
        description: `إلغاء إيراد فاتورة - ${invoice.customerName}`,
        relatedId: invoice.projectId,
        relatedName: invoice.customerName,
        date: new Date().toISOString().split('T')[0],
        paymentMethod: 'نقدي',
        notes: `إلغاء فاتورة تصنيع رقم ${invoice.id}`
      };
      addCashTransaction(transaction);
      
      loadInvoices();
      toast({
        title: "تم التراجع عن الفاتورة",
        description: "تم تحويل الفاتورة إلى مبدئية وإلغاء الإيراد من الخزينة",
      });
    }
  };

  const handleCompleteInvoice = () => {
    if (!selectedInvoice || !paymentAmount) return;

    const amount = parseFloat(paymentAmount);
    if (amount <= 0) {
      toast({
        title: "خطأ في المبلغ",
        description: "يرجى إدخال مبلغ صحيح",
        variant: "destructive",
      });
      return;
    }

    // تحديث حالة الفاتورة
    const allInvoices = getInvoices();
    const index = allInvoices.findIndex(inv => inv.id === selectedInvoice.id);
    if (index !== -1) {
      allInvoices[index] = { 
        ...selectedInvoice, 
        status: 'مكتملة',
        updatedAt: new Date().toISOString()
      };
      saveInvoices(allInvoices);
    }

    // تحديث المشروع
    const projects = getProjects();
    const projectIndex = projects.findIndex(p => p.id === selectedInvoice.projectId);
    if (projectIndex !== -1) {
      projects[projectIndex].paidAmount += amount;
      projects[projectIndex].remainingAmount -= amount;
      if (projects[projectIndex].remainingAmount <= 0) {
        projects[projectIndex].status = 'مكتمل';
        projects[projectIndex].completedAt = new Date().toISOString();
      }
      saveProjects(projects);
    }

    // إضافة معاملة دخل للخزينة
    const transaction: CashTransaction = {
      id: Date.now().toString(),
      type: 'دخل',
      category: 'مشروع',
      amount: amount,
      description: `دفعة نهائية من ${selectedInvoice.customerName}`,
      relatedId: selectedInvoice.projectId,
      relatedName: selectedInvoice.customerName,
      date: new Date().toISOString().split('T')[0],
      paymentMethod: 'نقدي',
      notes: paymentNotes || undefined
    };
    addCashTransaction(transaction);

    loadInvoices();
    setIsPaymentDialogOpen(false);
    setSelectedInvoice(null);
    setPaymentAmount("");
    setPaymentNotes("");

    toast({
      title: "تم إكمال الفاتورة",
      description: "تم تسجيل الدفعة النهائية وإكمال الفاتورة",
    });
  };

  const deleteInvoice = (invoiceId: string) => {
    const allInvoices = getInvoices();
    const updatedInvoices = allInvoices.filter(inv => inv.id !== invoiceId);
    saveInvoices(updatedInvoices);
    loadInvoices();
    
    toast({
      title: "تم حذف الفاتورة",
      description: "تم حذف الفاتورة بنجاح",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'مبدئية':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">مبدئية</Badge>;
      case 'تصنيع':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">تصنيع</Badge>;
      case 'مكتملة':
        return <Badge variant="default" className="bg-green-100 text-green-800">مكتملة</Badge>;
      case 'ملغية':
        return <Badge variant="destructive">ملغية</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50">
      <Navbar />
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-3 mb-8">
          <div className="p-3 bg-gradient-to-r from-blue-600 to-green-600 rounded-xl">
            <FileText className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الفواتير</h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع الفواتير والدفعات</p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              قائمة الفواتير
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>رقم الفاتورة</TableHead>
                    <TableHead>اسم العميل</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>النوع</TableHead>
                    <TableHead>تاريخ الإنشاء</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoices.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell className="font-medium">#{invoice.id}</TableCell>
                      <TableCell>{invoice.customerName}</TableCell>
                      <TableCell className="font-semibold text-green-600">
                        {formatLibyanDinar(invoice.totalAmount)}
                      </TableCell>
                      <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{invoice.type}</Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(invoice.createdAt).toLocaleDateString('ar-LY')}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {invoice.status === 'مبدئية' && (
                            <Button
                              size="sm"
                              onClick={() => handleConvertToManufacturing(invoice.id)}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              تحويل للتصنيع
                            </Button>
                          )}
                          
                          {invoice.status === 'تصنيع' && (
                            <>
                              <Button
                                size="sm"
                                onClick={() => {
                                  setSelectedInvoice(invoice);
                                  setIsPaymentDialogOpen(true);
                                }}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <DollarSign className="h-4 w-4 mr-1" />
                                دفعة نهائية
                              </Button>
                              
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button size="sm" variant="outline" className="text-orange-600 border-orange-600 hover:bg-orange-50">
                                    <RefreshCw className="h-4 w-4 mr-1" />
                                    تراجع
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle className="flex items-center gap-2">
                                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                                      تأكيد التراجع
                                    </AlertDialogTitle>
                                    <AlertDialogDescription>
                                      هل أنت متأكد من التراجع عن فاتورة التصنيع؟ سيتم إلغاء الإيراد من الخزينة وتحويل الفاتورة إلى مبدئية.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                    <AlertDialogAction onClick={() => handleRevertToInitial(invoice)}>
                                      تأكيد التراجع
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </>
                          )}

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditInvoice(invoice)}
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>

                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button size="sm" variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                                <AlertDialogDescription>
                                  هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                <AlertDialogAction onClick={() => deleteInvoice(invoice.id)}>
                                  حذف
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* حوار تعديل الفاتورة */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل الفاتورة</DialogTitle>
              <DialogDescription>
                تعديل بيانات الفاتورة
              </DialogDescription>
            </DialogHeader>
            {editingInvoice && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="customerName">اسم العميل</Label>
                  <Input
                    id="customerName"
                    value={editingInvoice.customerName}
                    onChange={(e) => setEditingInvoice({
                      ...editingInvoice,
                      customerName: e.target.value
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="totalAmount">المبلغ الإجمالي</Label>
                  <Input
                    id="totalAmount"
                    type="number"
                    value={editingInvoice.totalAmount}
                    onChange={(e) => setEditingInvoice({
                      ...editingInvoice,
                      totalAmount: parseFloat(e.target.value) || 0
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="notes">ملاحظات</Label>
                  <Textarea
                    id="notes"
                    value={editingInvoice.notes || ""}
                    onChange={(e) => setEditingInvoice({
                      ...editingInvoice,
                      notes: e.target.value
                    })}
                  />
                </div>
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleSaveEdit}>
                حفظ التعديلات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* حوار الدفعة النهائية */}
        <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>دفعة نهائية</DialogTitle>
              <DialogDescription>
                تسجيل الدفعة النهائية وإكمال الفاتورة
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="paymentAmount">مبلغ الدفعة (د.ل)</Label>
                <Input
                  id="paymentAmount"
                  type="number"
                  placeholder="أدخل مبلغ الدفعة"
                  value={paymentAmount}
                  onChange={(e) => setPaymentAmount(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="paymentNotes">ملاحظات</Label>
                <Textarea
                  id="paymentNotes"
                  placeholder="ملاحظات حول الدفعة"
                  value={paymentNotes}
                  onChange={(e) => setPaymentNotes(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleCompleteInvoice} className="bg-green-600 hover:bg-green-700">
                تأكيد الدفعة
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Invoices;
