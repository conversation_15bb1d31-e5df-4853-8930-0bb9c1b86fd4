import fs from 'fs';
import path from 'path';

console.log('🔍 فحص إعداد تطبيق سطح المكتب...\n');

// قائمة الملفات المطلوبة
const requiredFiles = [
  'public/electron.js',
  'public/preload.js',
  'package.json',
  'vite.config.ts',
  'src/App.tsx'
];

// قائمة المجلدات المطلوبة
const requiredDirs = [
  'src',
  'public',
  'src/components',
  'src/pages',
  'src/utils'
];

let allGood = true;

console.log('📁 فحص المجلدات المطلوبة:');
requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir}`);
  } else {
    console.log(`❌ ${dir} - مفقود`);
    allGood = false;
  }
});

console.log('\n📄 فحص الملفات المطلوبة:');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - مفقود`);
    allGood = false;
  }
});

// فحص package.json للتبعيات المطلوبة
console.log('\n📦 فحص التبعيات في package.json:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  const requiredDeps = ['electron', 'electron-builder', 'concurrently', 'wait-on'];
  const requiredScripts = ['electron', 'electron:dev', 'electron:pack', 'electron:dist'];
  
  requiredDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - مفقود من devDependencies`);
      allGood = false;
    }
  });
  
  console.log('\n⚙️ فحص Scripts:');
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`✅ ${script}: ${packageJson.scripts[script]}`);
    } else {
      console.log(`❌ ${script} - مفقود من scripts`);
      allGood = false;
    }
  });
  
  // فحص الإعدادات الأساسية
  console.log('\n🔧 فحص إعدادات Electron:');
  if (packageJson.main === 'public/electron.js') {
    console.log('✅ main: public/electron.js');
  } else {
    console.log('❌ main field غير صحيح');
    allGood = false;
  }
  
  if (packageJson.homepage === './') {
    console.log('✅ homepage: ./');
  } else {
    console.log('❌ homepage field غير صحيح');
    allGood = false;
  }
  
} catch (error) {
  console.log('❌ خطأ في قراءة package.json:', error.message);
  allGood = false;
}

// النتيجة النهائية
console.log('\n' + '='.repeat(50));
if (allGood) {
  console.log('🎉 ممتاز! جميع الملفات والإعدادات موجودة');
  console.log('🚀 يمكنك الآن تشغيل التطبيق باستخدام:');
  console.log('   npm install');
  console.log('   npm run electron:dev');
  console.log('\n📦 أو إنشاء ملف التثبيت:');
  console.log('   npm run build:desktop');
} else {
  console.log('⚠️ هناك ملفات أو إعدادات مفقودة');
  console.log('🔧 يرجى مراجعة الأخطاء أعلاه وإصلاحها');
}
console.log('='.repeat(50));
