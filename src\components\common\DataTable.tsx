// مكون جدول البيانات الموحد
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, Download, RefreshCw, Eye, Edit, Trash2 } from 'lucide-react';
import { themeClasses } from '@/styles/theme';
import { cn } from '@/lib/utils';

export interface TableColumn {
  key: string;
  title: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: any) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export interface TableAction {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: (row: any) => void;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  show?: (row: any) => boolean;
}

interface DataTableProps {
  title?: string;
  data: any[];
  columns: TableColumn[];
  actions?: TableAction[];
  searchable?: boolean;
  searchPlaceholder?: string;
  filterable?: boolean;
  exportable?: boolean;
  refreshable?: boolean;
  onRefresh?: () => void;
  onExport?: () => void;
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
  pageSize?: number;
}

const DataTable: React.FC<DataTableProps> = ({
  title,
  data,
  columns,
  actions = [],
  searchable = true,
  searchPlaceholder = 'البحث...',
  filterable = false,
  exportable = false,
  refreshable = false,
  onRefresh,
  onExport,
  loading = false,
  emptyMessage = 'لا توجد بيانات',
  className,
  pageSize = 10
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // تصفية البيانات
  const filteredData = data.filter(row => {
    if (!searchTerm) return true;
    
    return columns.some(column => {
      const value = row[column.key];
      if (value == null) return false;
      
      return value.toString().toLowerCase().includes(searchTerm.toLowerCase());
    });
  });

  // ترتيب البيانات
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortColumn) return 0;
    
    const aValue = a[sortColumn];
    const bValue = b[sortColumn];
    
    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return 1;
    if (bValue == null) return -1;
    
    const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    return sortDirection === 'asc' ? comparison : -comparison;
  });

  // تقسيم الصفحات
  const totalPages = Math.ceil(sortedData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedData = sortedData.slice(startIndex, startIndex + pageSize);

  // معالجة الترتيب
  const handleSort = (columnKey: string) => {
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  // معالجة الإجراءات
  const renderActions = (row: any) => {
    const visibleActions = actions.filter(action => 
      !action.show || action.show(row)
    );

    if (visibleActions.length === 0) return null;

    return (
      <div className="flex items-center gap-2">
        {visibleActions.map(action => {
          const Icon = action.icon;
          return (
            <Button
              key={action.key}
              size="sm"
              variant={action.variant || 'outline'}
              onClick={() => action.onClick(row)}
              className="h-8 w-8 p-0"
              title={action.label}
            >
              <Icon className="h-4 w-4" />
            </Button>
          );
        })}
      </div>
    );
  };

  return (
    <Card className={cn('shadow-lg', className)}>
      {/* رأس الجدول */}
      <CardHeader>
        <div className={themeClasses.flexBetween}>
          <div>
            {title && (
              <CardTitle className="flex items-center gap-2">
                {title}
                <Badge variant="secondary">
                  {filteredData.length}
                </Badge>
              </CardTitle>
            )}
          </div>

          {/* أدوات التحكم */}
          <div className="flex items-center gap-2">
            {refreshable && onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
              >
                <RefreshCw className={cn(
                  'h-4 w-4',
                  loading && 'animate-spin'
                )} />
              </Button>
            )}

            {exportable && onExport && (
              <Button
                variant="outline"
                size="sm"
                onClick={onExport}
              >
                <Download className="h-4 w-4" />
              </Button>
            )}

            {filterable && (
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* شريط البحث */}
        {searchable && (
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10"
            />
          </div>
        )}
      </CardHeader>

      {/* محتوى الجدول */}
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : paginatedData.length === 0 ? (
          <div className="text-center py-8">
            <p className={themeClasses.bodyText}>{emptyMessage}</p>
          </div>
        ) : (
          <>
            {/* الجدول */}
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {columns.map(column => (
                      <TableHead
                        key={column.key}
                        className={cn(
                          'text-right',
                          column.sortable && 'cursor-pointer hover:bg-gray-50',
                          column.width && `w-${column.width}`
                        )}
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-2">
                          {column.title}
                          {column.sortable && sortColumn === column.key && (
                            <span className="text-xs">
                              {sortDirection === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </TableHead>
                    ))}
                    {actions.length > 0 && (
                      <TableHead className="text-right">الإجراءات</TableHead>
                    )}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedData.map((row, index) => (
                    <TableRow key={row.id || index}>
                      {columns.map(column => (
                        <TableCell
                          key={column.key}
                          className={cn(
                            column.align === 'center' && 'text-center',
                            column.align === 'left' && 'text-left',
                            column.align === 'right' && 'text-right'
                          )}
                        >
                          {column.render 
                            ? column.render(row[column.key], row)
                            : row[column.key]
                          }
                        </TableCell>
                      ))}
                      {actions.length > 0 && (
                        <TableCell>
                          {renderActions(row)}
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* التنقل بين الصفحات */}
            {totalPages > 1 && (
              <div className={cn(themeClasses.flexBetween, 'mt-4')}>
                <div className={themeClasses.smallText}>
                  عرض {startIndex + 1} إلى {Math.min(startIndex + pageSize, sortedData.length)} من {sortedData.length}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    السابق
                  </Button>
                  <span className={themeClasses.smallText}>
                    {currentPage} من {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    التالي
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default DataTable;
