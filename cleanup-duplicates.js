#!/usr/bin/env node

// سكريبت تنظيف الملفات المكررة والتحسينات
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧹 بدء عملية تنظيف المشروع...\n');

// قائمة الملفات المكررة أو غير المستخدمة
const filesToRemove = [
  // ملفات التوثيق المكررة
  'تعليمات_اختبار_الطباعة.md',
  'تعليمات_اختبار_الطباعة_المحدثة.md',
  
  // ملفات التقارير القديمة
  'تقرير_إصلاح_خطأ_reduce.md',
  
  // ملفات البناء المؤقتة
  'build-electron.js',
  
  // ملفات الاختبار المؤقتة
  'test-setup.js'
];

// قائمة المجلدات المؤقتة للتنظيف
const foldersToClean = [
  'dist',
  'dist-electron',
  'dist-electron-main',
  'node_modules/.cache'
];

// تنظيف الملفات
console.log('📄 تنظيف الملفات المكررة:');
filesToRemove.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`✅ تم حذف: ${file}`);
    } catch (error) {
      console.log(`❌ فشل في حذف: ${file} - ${error.message}`);
    }
  } else {
    console.log(`ℹ️  غير موجود: ${file}`);
  }
});

// تنظيف المجلدات المؤقتة
console.log('\n📁 تنظيف المجلدات المؤقتة:');
foldersToClean.forEach(folder => {
  const folderPath = path.join(__dirname, folder);
  if (fs.existsSync(folderPath)) {
    try {
      fs.rmSync(folderPath, { recursive: true, force: true });
      console.log(`✅ تم حذف المجلد: ${folder}`);
    } catch (error) {
      console.log(`❌ فشل في حذف المجلد: ${folder} - ${error.message}`);
    }
  } else {
    console.log(`ℹ️  غير موجود: ${folder}`);
  }
});

// تحديث package.json لإزالة التبعيات غير المستخدمة
console.log('\n📦 فحص package.json:');
try {
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // إزالة التبعيات غير المستخدمة
  const unusedDependencies = [
    'lovable-tagger' // قد لا نحتاجها في الإنتاج
  ];
  
  let updated = false;
  unusedDependencies.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      delete packageJson.devDependencies[dep];
      console.log(`✅ تم إزالة التبعية: ${dep}`);
      updated = true;
    }
  });
  
  // تحديث الإصدار
  packageJson.version = '1.0.1';
  
  // تحسين scripts
  if (packageJson.scripts) {
    // إضافة script للتنظيف
    packageJson.scripts.cleanup = 'node cleanup-duplicates.js';
    
    // تحسين script البناء
    packageJson.scripts['build:clean'] = 'npm run cleanup && npm run build';
    
    updated = true;
  }
  
  if (updated) {
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ تم تحديث package.json');
  } else {
    console.log('ℹ️  لا توجد تحديثات مطلوبة في package.json');
  }
  
} catch (error) {
  console.log(`❌ خطأ في تحديث package.json: ${error.message}`);
}

// تنظيف ملفات TypeScript المؤقتة
console.log('\n🔧 تنظيف ملفات TypeScript:');
const tsConfigFiles = [
  'tsconfig.tsbuildinfo'
];

tsConfigFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`✅ تم حذف: ${file}`);
    } catch (error) {
      console.log(`❌ فشل في حذف: ${file} - ${error.message}`);
    }
  }
});

// إنشاء ملف .gitignore محسن
console.log('\n📝 تحديث .gitignore:');
const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
dist-electron/
dist-electron-main/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Logs
logs/
*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
.cache/

# Coverage reports
coverage/
*.lcov

# Electron
app/
release/
`;

try {
  fs.writeFileSync(path.join(__dirname, '.gitignore'), gitignoreContent);
  console.log('✅ تم تحديث .gitignore');
} catch (error) {
  console.log(`❌ فشل في تحديث .gitignore: ${error.message}`);
}

// إنشاء ملف README محسن
console.log('\n📖 إنشاء README محسن:');
const readmeContent = `# نظام إدارة مصنع الأثاث

نظام شامل لإدارة مصنع الأثاث والتكاليف مبني بتقنيات حديثة.

## المميزات

- 🧮 حاسبة التكلفة الذكية
- 📦 إدارة المواد والمخزون
- 👥 إدارة العمال والموظفين
- 🏭 إدارة المصانع والمصممين
- 💰 إدارة الخزينة والمالية
- 📊 التقارير والإحصائيات
- 🖨️ نظام الطباعة المتقدم
- 🔔 نظام التنبيهات

## التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript
- **UI**: Tailwind CSS + shadcn/ui
- **Desktop**: Electron
- **Database**: SQLite
- **Build**: Vite
- **State Management**: React Query + Custom State Manager

## التشغيل

### متطلبات النظام
- Node.js 18+ 
- npm أو yarn

### التثبيت والتشغيل
\`\`\`bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm run electron:dev

# بناء التطبيق للإنتاج
npm run build

# إنشاء ملف التثبيت
npm run electron:dist
\`\`\`

## البنية

\`\`\`
src/
├── components/     # المكونات القابلة لإعادة الاستخدام
├── pages/         # صفحات التطبيق
├── services/      # خدمات البيانات والAPI
├── utils/         # الوظائف المساعدة
├── types/         # تعريفات TypeScript
├── config/        # إعدادات التطبيق
└── hooks/         # React Hooks مخصصة
\`\`\`

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
`;

try {
  fs.writeFileSync(path.join(__dirname, 'README.md'), readmeContent);
  console.log('✅ تم إنشاء README.md');
} catch (error) {
  console.log(`❌ فشل في إنشاء README.md: ${error.message}`);
}

// تقرير نهائي
console.log('\n🎉 تم الانتهاء من عملية التنظيف!');
console.log('\n📋 ملخص العمليات:');
console.log('✅ تم حذف الملفات المكررة');
console.log('✅ تم تنظيف المجلدات المؤقتة');
console.log('✅ تم تحديث package.json');
console.log('✅ تم تحديث .gitignore');
console.log('✅ تم إنشاء README.md');

console.log('\n🚀 الخطوات التالية:');
console.log('1. تشغيل: npm install');
console.log('2. تشغيل: npm run electron:dev');
console.log('3. اختبار جميع الوظائف');

console.log('\n💡 نصائح:');
console.log('- استخدم npm run cleanup لتنظيف المشروع');
console.log('- استخدم npm run build:clean للبناء مع التنظيف');
console.log('- تحقق من عدم وجود أخطاء في الكونسول');
