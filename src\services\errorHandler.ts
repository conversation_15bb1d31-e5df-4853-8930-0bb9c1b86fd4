// خدمة معالجة الأخطاء الموحدة
// تهدف لتوحيد معالجة الأخطاء وتسجيلها عبر التطبيق

import { APP_CONFIG } from '@/config/appConfig';

// أنواع الأخطاء
export enum ErrorType {
  NETWORK = 'network',
  DATABASE = 'database',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  NOT_FOUND = 'not_found',
  DUPLICATE = 'duplicate',
  UNKNOWN = 'unknown'
}

// مستويات الخطورة
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// واجهة الخطأ المخصص
export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  context?: string;
  timestamp: Date;
  stack?: string;
  userId?: string;
  sessionId?: string;
}

// مستمعي الأخطاء
type ErrorListener = (error: AppError) => void;
const errorListeners: ErrorListener[] = [];

// سجل الأخطاء
const errorLog: AppError[] = [];

// إضافة مستمع للأخطاء
export const addErrorListener = (listener: ErrorListener): (() => void) => {
  errorListeners.push(listener);
  
  return () => {
    const index = errorListeners.indexOf(listener);
    if (index > -1) {
      errorListeners.splice(index, 1);
    }
  };
};

// إنشاء معرف فريد للخطأ
const generateErrorId = (): string => {
  return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// تحديد نوع الخطأ تلقائياً
const determineErrorType = (error: any): ErrorType => {
  if (error?.name === 'NetworkError' || error?.code === 'NETWORK_ERROR') {
    return ErrorType.NETWORK;
  }
  
  if (error?.name === 'DatabaseError' || error?.message?.includes('database')) {
    return ErrorType.DATABASE;
  }
  
  if (error?.name === 'ValidationError' || error?.message?.includes('validation')) {
    return ErrorType.VALIDATION;
  }
  
  if (error?.name === 'PermissionError' || error?.code === 'PERMISSION_DENIED') {
    return ErrorType.PERMISSION;
  }
  
  if (error?.name === 'NotFoundError' || error?.code === 'NOT_FOUND') {
    return ErrorType.NOT_FOUND;
  }
  
  if (error?.name === 'DuplicateError' || error?.code === 'DUPLICATE') {
    return ErrorType.DUPLICATE;
  }
  
  return ErrorType.UNKNOWN;
};

// تحديد مستوى الخطورة
const determineSeverity = (type: ErrorType): ErrorSeverity => {
  switch (type) {
    case ErrorType.CRITICAL:
    case ErrorType.DATABASE:
      return ErrorSeverity.CRITICAL;
    
    case ErrorType.NETWORK:
    case ErrorType.PERMISSION:
      return ErrorSeverity.HIGH;
    
    case ErrorType.NOT_FOUND:
    case ErrorType.DUPLICATE:
      return ErrorSeverity.MEDIUM;
    
    case ErrorType.VALIDATION:
    default:
      return ErrorSeverity.LOW;
  }
};

// إنشاء خطأ مخصص
export const createAppError = (
  error: any,
  context?: string,
  type?: ErrorType,
  severity?: ErrorSeverity
): AppError => {
  const errorType = type || determineErrorType(error);
  const errorSeverity = severity || determineSeverity(errorType);
  
  return {
    id: generateErrorId(),
    type: errorType,
    severity: errorSeverity,
    message: error?.message || error?.toString() || 'خطأ غير محدد',
    details: error,
    context: context || 'غير محدد',
    timestamp: new Date(),
    stack: error?.stack,
    userId: getCurrentUserId(),
    sessionId: getCurrentSessionId()
  };
};

// تسجيل الخطأ
export const logError = (appError: AppError): void => {
  // إضافة للسجل المحلي
  errorLog.push(appError);
  
  // الحفاظ على حد أقصى لحجم السجل
  if (errorLog.length > APP_CONFIG.logging.maxFiles * 1000) {
    errorLog.splice(0, errorLog.length - APP_CONFIG.logging.maxFiles * 1000);
  }
  
  // طباعة في الكونسول حسب مستوى الخطورة
  switch (appError.severity) {
    case ErrorSeverity.CRITICAL:
      console.error('🔴 خطأ حرج:', appError);
      break;
    case ErrorSeverity.HIGH:
      console.error('🟠 خطأ عالي:', appError);
      break;
    case ErrorSeverity.MEDIUM:
      console.warn('🟡 خطأ متوسط:', appError);
      break;
    case ErrorSeverity.LOW:
    default:
      console.log('🔵 خطأ منخفض:', appError);
      break;
  }
  
  // إشعار المستمعين
  errorListeners.forEach(listener => {
    try {
      listener(appError);
    } catch (listenerError) {
      console.error('خطأ في مستمع الأخطاء:', listenerError);
    }
  });
};

// معالجة الخطأ الرئيسية
export const handleError = (
  error: any,
  context?: string,
  type?: ErrorType,
  severity?: ErrorSeverity
): AppError => {
  const appError = createAppError(error, context, type, severity);
  logError(appError);
  return appError;
};

// معالجة الأخطاء غير المتزامنة
export const handleAsyncError = async (
  asyncOperation: () => Promise<any>,
  context?: string
): Promise<{ success: boolean; data?: any; error?: AppError }> => {
  try {
    const data = await asyncOperation();
    return { success: true, data };
  } catch (error) {
    const appError = handleError(error, context);
    return { success: false, error: appError };
  }
};

// معالجة أخطاء React
export const handleReactError = (error: Error, errorInfo: any): void => {
  const appError = createAppError(
    {
      ...error,
      componentStack: errorInfo.componentStack
    },
    'React Component',
    ErrorType.UNKNOWN,
    ErrorSeverity.HIGH
  );
  
  logError(appError);
};

// الحصول على الأخطاء المسجلة
export const getErrorLog = (
  filters?: {
    type?: ErrorType;
    severity?: ErrorSeverity;
    context?: string;
    fromDate?: Date;
    toDate?: Date;
    limit?: number;
  }
): AppError[] => {
  let filteredErrors = [...errorLog];
  
  if (filters) {
    if (filters.type) {
      filteredErrors = filteredErrors.filter(e => e.type === filters.type);
    }
    
    if (filters.severity) {
      filteredErrors = filteredErrors.filter(e => e.severity === filters.severity);
    }
    
    if (filters.context) {
      filteredErrors = filteredErrors.filter(e => 
        e.context?.toLowerCase().includes(filters.context!.toLowerCase())
      );
    }
    
    if (filters.fromDate) {
      filteredErrors = filteredErrors.filter(e => e.timestamp >= filters.fromDate!);
    }
    
    if (filters.toDate) {
      filteredErrors = filteredErrors.filter(e => e.timestamp <= filters.toDate!);
    }
    
    if (filters.limit) {
      filteredErrors = filteredErrors.slice(-filters.limit);
    }
  }
  
  return filteredErrors.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
};

// مسح سجل الأخطاء
export const clearErrorLog = (): void => {
  errorLog.length = 0;
};

// تصدير سجل الأخطاء
export const exportErrorLog = (): string => {
  return JSON.stringify(errorLog, null, 2);
};

// الحصول على إحصائيات الأخطاء
export const getErrorStats = () => {
  const stats = {
    total: errorLog.length,
    byType: {} as Record<ErrorType, number>,
    bySeverity: {} as Record<ErrorSeverity, number>,
    recent: errorLog.filter(e => 
      Date.now() - e.timestamp.getTime() < 24 * 60 * 60 * 1000
    ).length
  };
  
  errorLog.forEach(error => {
    stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
  });
  
  return stats;
};

// دوال مساعدة للحصول على معلومات المستخدم والجلسة
const getCurrentUserId = (): string | undefined => {
  // يمكن تنفيذ هذا حسب نظام المصادقة المستخدم
  return undefined;
};

const getCurrentSessionId = (): string | undefined => {
  // يمكن تنفيذ هذا حسب نظام إدارة الجلسات
  return undefined;
};

// رسائل خطأ مترجمة
export const getErrorMessage = (type: ErrorType): string => {
  switch (type) {
    case ErrorType.NETWORK:
      return APP_CONFIG.errorMessages.networkError;
    case ErrorType.DATABASE:
      return APP_CONFIG.errorMessages.databaseError;
    case ErrorType.VALIDATION:
      return APP_CONFIG.errorMessages.validationError;
    case ErrorType.PERMISSION:
      return APP_CONFIG.errorMessages.permissionError;
    case ErrorType.NOT_FOUND:
      return APP_CONFIG.errorMessages.notFoundError;
    case ErrorType.DUPLICATE:
      return APP_CONFIG.errorMessages.duplicateError;
    default:
      return APP_CONFIG.errorMessages.unknownError;
  }
};

// تهيئة معالج الأخطاء العام
export const initializeErrorHandler = (): void => {
  // معالج الأخطاء غير المعالجة
  window.addEventListener('error', (event) => {
    handleError(event.error, 'Global Error Handler');
  });
  
  // معالج الوعود المرفوضة
  window.addEventListener('unhandledrejection', (event) => {
    handleError(event.reason, 'Unhandled Promise Rejection');
  });
  
  console.log('تم تهيئة معالج الأخطاء العام');
};
