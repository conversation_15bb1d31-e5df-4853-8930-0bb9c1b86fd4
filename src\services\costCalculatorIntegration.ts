// خدمة تكامل حاسبة التكلفة مع جميع أقسام التطبيق
import { ProjectMaterialsSummary, MaterialCalculation } from '@/types/materials';
import { ProjectCostBreakdown, LaborCostIntegration, TreasuryIntegration } from '@/types/integration';
import IntegrationService from './integrationService';
import { MaterialService, WorkerService, FactoryService, DesignerService, ProjectService } from './databaseService';
import { getDetailedMaterials, updateDetailedMaterial } from './materialsService';
import { formatLibyanDinar } from '@/utils/calculations';

// التحقق من وجود Electron API
const isElectron = typeof window !== 'undefined' && window.electronAPI;

export class CostCalculatorIntegration {
  
  // حساب التكلفة المتكاملة للمشروع
  static async calculateIntegratedProjectCost(projectData: {
    area: number;
    furnitureType: string;
    selectedMaterial: any;
    selectedWorker: any;
    selectedFactory: any;
    selectedDesigner: any;
    materialsSummary?: ProjectMaterialsSummary;
  }): Promise<ProjectCostBreakdown> {
    
    try {
      // حساب تكلفة المواد
      const materialsCost = await this.calculateMaterialsCost(projectData.materialsSummary, projectData.area);
      
      // حساب تكلفة العمالة
      const laborCost = await this.calculateLaborCost(projectData);
      
      // حساب التكاليف الإضافية
      const overheadCost = await this.calculateOverheadCost(projectData.area);
      
      // إجمالي تكلفة المشروع
      const totalProjectCost = materialsCost.total + laborCost.total + overheadCost.total;
      
      // هامش الربح (20% افتراضي)
      const profitMargin = totalProjectCost * 0.20;
      const finalPrice = totalProjectCost + profitMargin;

      const breakdown: ProjectCostBreakdown = {
        projectId: Date.now().toString(),
        materialsCost,
        laborCost,
        overheadCost,
        totalProjectCost,
        profitMargin,
        finalPrice
      };

      // إرسال حدث تكامل
      await IntegrationService.addIntegrationEvent({
        type: 'cost_calculated',
        source: 'cost_calculator',
        target: ['materials', 'treasury', 'salaries', 'reports'],
        data: {
          projectData,
          breakdown,
          materialsSummary: projectData.materialsSummary
        }
      });

      return breakdown;
    } catch (error) {
      console.error('خطأ في حساب التكلفة المتكاملة:', error);
      throw error;
    }
  }

  // حساب تكلفة المواد
  private static async calculateMaterialsCost(materialsSummary?: ProjectMaterialsSummary, area: number = 0): Promise<{
    basic: number;
    detailed: number;
    total: number;
  }> {
    let basicCost = 0;
    let detailedCost = 0;

    if (materialsSummary) {
      detailedCost = materialsSummary.totalSaleCost;
      
      // التحقق من توفر المواد وإرسال تنبيهات
      await this.checkMaterialAvailability(materialsSummary);
    }

    return {
      basic: basicCost,
      detailed: detailedCost,
      total: basicCost + detailedCost
    };
  }

  // حساب تكلفة العمالة
  private static async calculateLaborCost(projectData: any): Promise<{
    workers: number;
    designers: number;
    factory: number;
    total: number;
  }> {
    const area = projectData.area;
    
    const workersCost = area * (projectData.selectedWorker?.pricePerSqm || 0);
    const designersCost = area * (projectData.selectedDesigner?.pricePerSqm || 0);
    const factoryCost = area * (projectData.selectedFactory?.pricePerSqm || 0);

    // حساب ساعات العمل المقدرة
    const estimatedHours = await this.calculateEstimatedWorkHours(area, projectData.furnitureType);
    
    // إنشاء تكامل العمالة
    if (projectData.selectedWorker) {
      const laborIntegration: LaborCostIntegration = {
        workerId: projectData.selectedWorker.id,
        projectId: projectData.id || Date.now().toString(),
        estimatedHours,
        hourlyRate: projectData.selectedWorker.pricePerSqm / 8, // تقدير 8 ساعات عمل لكل متر مربع
        totalLaborCost: workersCost,
        skillLevel: projectData.selectedWorker.specialty,
        availability: true
      };

      await this.saveLaborIntegration(laborIntegration);
    }

    return {
      workers: workersCost,
      designers: designersCost,
      factory: factoryCost,
      total: workersCost + designersCost + factoryCost
    };
  }

  // حساب التكاليف الإضافية
  private static async calculateOverheadCost(area: number): Promise<{
    utilities: number;
    rent: number;
    equipment: number;
    total: number;
  }> {
    // تكاليف ثابتة لكل متر مربع
    const utilitiesPerSqm = 5.0; // كهرباء وماء
    const rentPerSqm = 3.0; // إيجار المصنع
    const equipmentPerSqm = 2.0; // استهلاك المعدات

    const utilities = area * utilitiesPerSqm;
    const rent = area * rentPerSqm;
    const equipment = area * equipmentPerSqm;

    return {
      utilities,
      rent,
      equipment,
      total: utilities + rent + equipment
    };
  }

  // حساب ساعات العمل المقدرة
  private static async calculateEstimatedWorkHours(area: number, furnitureType: string): Promise<number> {
    // معدلات ساعات العمل حسب نوع الأثاث
    const hourRates: { [key: string]: number } = {
      'مطبخ': 12, // 12 ساعة لكل متر مربع
      'غرفة نوم': 8,
      'غرفة جلوس': 6,
      'مكتب': 4,
      'خزانة': 10,
      'أخرى': 6
    };

    const ratePerSqm = hourRates[furnitureType] || hourRates['أخرى'];
    return area * ratePerSqm;
  }

  // التحقق من توفر المواد
  private static async checkMaterialAvailability(materialsSummary: ProjectMaterialsSummary): Promise<void> {
    try {
      for (const material of materialsSummary.materials) {
        if (material.source === 'stock') {
          // جلب بيانات المادة من قاعدة البيانات
          const detailedMaterial = await this.getMaterialById(material.materialId);
          
          if (detailedMaterial) {
            if (detailedMaterial.availableQuantity < material.requiredQuantity) {
              // إرسال تنبيه نقص المواد
              await IntegrationService.addNotification({
                type: 'warning',
                title: 'نقص في المواد',
                message: `المادة ${detailedMaterial.name} غير متوفرة بالكمية المطلوبة. متوفر: ${detailedMaterial.availableQuantity}، مطلوب: ${material.requiredQuantity}`,
                source: 'cost_calculator',
                priority: 'high',
                actionRequired: true,
                relatedId: material.materialId
              });
            } else if (detailedMaterial.availableQuantity <= detailedMaterial.minQuantity) {
              // تنبيه اقتراب من الحد الأدنى
              await IntegrationService.addNotification({
                type: 'warning',
                title: 'اقتراب من الحد الأدنى',
                message: `المادة ${detailedMaterial.name} تقترب من الحد الأدنى. الكمية الحالية: ${detailedMaterial.availableQuantity}`,
                source: 'cost_calculator',
                priority: 'medium',
                actionRequired: true,
                relatedId: material.materialId
              });
            }
          }
        }
      }
    } catch (error) {
      console.error('خطأ في التحقق من توفر المواد:', error);
    }
  }

  // جلب بيانات المادة
  private static async getMaterialById(materialId: string): Promise<any> {
    try {
      if (isElectron) {
        return await window.electronAPI.getQuery(
          'SELECT * FROM detailed_materials WHERE id = ?',
          [materialId]
        );
      }
      return null;
    } catch (error) {
      console.error('خطأ في جلب بيانات المادة:', error);
      return null;
    }
  }

  // حفظ تكامل العمالة
  private static async saveLaborIntegration(integration: LaborCostIntegration): Promise<void> {
    try {
      if (isElectron) {
        await window.electronAPI.runQuery(
          `INSERT INTO labor_integrations 
           (id, worker_id, project_id, estimated_hours, hourly_rate, total_labor_cost, skill_level, availability, created_at) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            Date.now().toString(),
            integration.workerId,
            integration.projectId,
            integration.estimatedHours,
            integration.hourlyRate,
            integration.totalLaborCost,
            integration.skillLevel,
            integration.availability ? 1 : 0,
            new Date().toISOString()
          ]
        );
      }
    } catch (error) {
      console.error('خطأ في حفظ تكامل العمالة:', error);
    }
  }

  // تحديث أسعار المواد من قاعدة البيانات
  static async updateMaterialPrices(materialsSummary: ProjectMaterialsSummary): Promise<ProjectMaterialsSummary> {
    try {
      const updatedMaterials: MaterialCalculation[] = [];

      for (const material of materialsSummary.materials) {
        const detailedMaterial = await this.getMaterialById(material.materialId);
        
        if (detailedMaterial) {
          const updatedMaterial: MaterialCalculation = {
            ...material,
            purchasePrice: detailedMaterial.purchasePrice,
            salePrice: detailedMaterial.salePrice,
            totalPurchase: material.requiredQuantity * detailedMaterial.purchasePrice,
            totalSale: material.requiredQuantity * detailedMaterial.salePrice,
            profit: (material.requiredQuantity * detailedMaterial.salePrice) - (material.requiredQuantity * detailedMaterial.purchasePrice)
          };
          updatedMaterials.push(updatedMaterial);
        } else {
          updatedMaterials.push(material);
        }
      }

      // إعادة حساب الإجماليات
      const totalPurchaseCost = updatedMaterials.reduce((sum, m) => sum + m.totalPurchase, 0);
      const totalSaleCost = updatedMaterials.reduce((sum, m) => sum + m.totalSale, 0);
      const totalProfit = totalSaleCost - totalPurchaseCost;
      const externalCost = updatedMaterials.filter(m => m.source === 'external').reduce((sum, m) => sum + m.totalPurchase, 0);
      const stockCost = updatedMaterials.filter(m => m.source === 'stock').reduce((sum, m) => sum + m.totalPurchase, 0);

      return {
        ...materialsSummary,
        materials: updatedMaterials,
        totalPurchaseCost,
        totalSaleCost,
        totalProfit,
        externalCost,
        stockCost
      };
    } catch (error) {
      console.error('خطأ في تحديث أسعار المواد:', error);
      return materialsSummary;
    }
  }

  // إنشاء تقرير تكلفة مفصل
  static async generateCostReport(breakdown: ProjectCostBreakdown): Promise<string> {
    const report = `
تقرير تكلفة المشروع
==================

معرف المشروع: ${breakdown.projectId}
تاريخ التقرير: ${new Date().toLocaleDateString('ar-LY')}

تفصيل التكاليف:
--------------

1. تكلفة المواد:
   - المواد الأساسية: ${formatLibyanDinar(breakdown.materialsCost.basic)}
   - المواد التفصيلية: ${formatLibyanDinar(breakdown.materialsCost.detailed)}
   - إجمالي المواد: ${formatLibyanDinar(breakdown.materialsCost.total)}

2. تكلفة العمالة:
   - العمال: ${formatLibyanDinar(breakdown.laborCost.workers)}
   - المصممين: ${formatLibyanDinar(breakdown.laborCost.designers)}
   - المصانع: ${formatLibyanDinar(breakdown.laborCost.factory)}
   - إجمالي العمالة: ${formatLibyanDinar(breakdown.laborCost.total)}

3. التكاليف الإضافية:
   - المرافق: ${formatLibyanDinar(breakdown.overheadCost.utilities)}
   - الإيجار: ${formatLibyanDinar(breakdown.overheadCost.rent)}
   - المعدات: ${formatLibyanDinar(breakdown.overheadCost.equipment)}
   - إجمالي التكاليف الإضافية: ${formatLibyanDinar(breakdown.overheadCost.total)}

الملخص النهائي:
--------------
إجمالي تكلفة المشروع: ${formatLibyanDinar(breakdown.totalProjectCost)}
هامش الربح: ${formatLibyanDinar(breakdown.profitMargin)}
السعر النهائي: ${formatLibyanDinar(breakdown.finalPrice)}

نسبة الربح: ${((breakdown.profitMargin / breakdown.totalProjectCost) * 100).toFixed(2)}%
`;

    return report;
  }

  // حفظ تفصيل التكلفة في قاعدة البيانات
  static async saveCostBreakdown(breakdown: ProjectCostBreakdown): Promise<void> {
    try {
      if (isElectron) {
        await window.electronAPI.runQuery(
          `INSERT OR REPLACE INTO project_cost_breakdowns 
           (project_id, materials_cost, labor_cost, overhead_cost, total_cost, profit_margin, final_price, created_at) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            breakdown.projectId,
            JSON.stringify(breakdown.materialsCost),
            JSON.stringify(breakdown.laborCost),
            JSON.stringify(breakdown.overheadCost),
            breakdown.totalProjectCost,
            breakdown.profitMargin,
            breakdown.finalPrice,
            new Date().toISOString()
          ]
        );
      }
    } catch (error) {
      console.error('خطأ في حفظ تفصيل التكلفة:', error);
    }
  }
}

export default CostCalculatorIntegration;
