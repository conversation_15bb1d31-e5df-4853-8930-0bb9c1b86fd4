// حاسبة التكلفة المحسنة
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Calculator, 
  Package, 
  User, 
  Factory as FactoryIcon, 
  Palette, 
  Save, 
  FileText,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  DollarSign
} from 'lucide-react';

import FormField from '@/components/common/FormField';
import { LoadingState, ErrorState, DataState } from '@/components/common/LoadingState';
import { themeClasses } from '@/styles/theme';
import { cn } from '@/lib/utils';
import { 
  handleError, 
  handleSuccess, 
  validateRequired, 
  validateNumber,
  formatCurrency,
  safeParseNumber
} from '@/utils/commonUtils';

import {
  getMaterials,
  getWorkers,
  getFactories,
  getDesigners,
  addProject,
  Material,
  Worker,
  Factory,
  Designer,
  Project
} from '@/utils/dataManager';

import {
  DetailedMaterial,
  MaterialCategory,
  MaterialCalculation,
  ProjectMaterialsSummary
} from '@/types/materials';

import {
  getDetailedMaterials,
  getMaterialCategories,
  calculateMaterialCosts
} from '@/services/materialsService';

import MaterialSelector from '../MaterialSelector';
import SelectedMaterialsTable from '../SelectedMaterialsTable';

// واجهات البيانات
interface CostBreakdown {
  materials: number;
  labor: number;
  factory: number;
  design: number;
  subtotal: number;
  tax: number;
  total: number;
  profitMargin: number;
  finalPrice: number;
}

interface ProjectData {
  customerName: string;
  phone: string;
  area: number;
  furnitureType: string;
  selectedMaterials: MaterialCalculation[];
  materialsSummary: ProjectMaterialsSummary | null;
  selectedWorker: Worker | null;
  selectedFactory: Factory | null;
  selectedDesigner: Designer | null;
  notes: string;
  taxRate: number;
  profitMarginRate: number;
}

interface EnhancedCostCalculatorProps {
  onStatsUpdate?: () => void;
}

const EnhancedCostCalculator: React.FC<EnhancedCostCalculatorProps> = ({ 
  onStatsUpdate 
}) => {
  // حالة البيانات
  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [saving, setSaving] = useState(false);
  
  // بيانات المشروع
  const [projectData, setProjectData] = useState<ProjectData>({
    customerName: '',
    phone: '',
    area: 0,
    furnitureType: '',
    selectedMaterials: [],
    materialsSummary: null,
    selectedWorker: null,
    selectedFactory: null,
    selectedDesigner: null,
    notes: '',
    taxRate: 0.1, // 10% ضريبة افتراضية
    profitMarginRate: 0.2 // 20% هامش ربح افتراضي
  });

  // بيانات الخيارات
  const [materials, setMaterials] = useState<Material[]>([]);
  const [workers, setWorkers] = useState<Worker[]>([]);
  const [factories, setFactories] = useState<Factory[]>([]);
  const [designers, setDesigners] = useState<Designer[]>([]);

  // نتائج الحساب
  const [costBreakdown, setCostBreakdown] = useState<CostBreakdown | null>(null);
  
  // أخطاء التحقق
  const [errors, setErrors] = useState<Record<string, string>>({});

  // تحميل البيانات الأولية
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      const [materialsData, workersData, factoriesData, designersData] = await Promise.all([
        getMaterials(),
        getWorkers(),
        getFactories(),
        getDesigners()
      ]);

      setMaterials(materialsData);
      setWorkers(workersData);
      setFactories(factoriesData);
      setDesigners(designersData);
    } catch (error) {
      handleError(error, 'تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  // التحقق من صحة البيانات
  const validateProjectData = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    try {
      validateRequired(projectData.customerName, 'اسم العميل');
      validateRequired(projectData.phone, 'رقم الهاتف');
      validateNumber(projectData.area, 'المساحة', 0.1);
      validateRequired(projectData.furnitureType, 'نوع الأثاث');
      
      if (!projectData.selectedMaterials || projectData.selectedMaterials.length === 0) {
        newErrors.selectedMaterials = 'يجب اختيار مادة واحدة على الأقل';
      }
      if (!projectData.selectedWorker) {
        newErrors.selectedWorker = 'يجب اختيار عامل';
      }
      if (!projectData.selectedFactory) {
        newErrors.selectedFactory = 'يجب اختيار مصنع';
      }
      if (!projectData.selectedDesigner) {
        newErrors.selectedDesigner = 'يجب اختيار مصمم';
      }

      validateNumber(projectData.taxRate, 'معدل الضريبة', 0, 1);
      validateNumber(projectData.profitMarginRate, 'هامش الربح', 0, 1);
      
    } catch (error: any) {
      const fieldName = error.message.split(':')[0];
      newErrors[fieldName] = error.message;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [projectData]);

  // حساب التكلفة
  const calculateCost = useCallback((): CostBreakdown | null => {
    if (!validateProjectData()) return null;

    const { area, materialsSummary, selectedWorker, selectedFactory, selectedDesigner, taxRate, profitMarginRate } = projectData;

    // حساب التكاليف الأساسية
    const materials = materialsSummary?.totalCost || 0;
    const labor = area * (selectedWorker?.pricePerSqm || 0);
    const factory = area * (selectedFactory?.pricePerSqm || 0);
    const design = area * (selectedDesigner?.pricePerSqm || 0);

    const subtotal = materials + labor + factory + design;
    const tax = subtotal * taxRate;
    const total = subtotal + tax;
    const profitMargin = total * profitMarginRate;
    const finalPrice = total + profitMargin;

    return {
      materials,
      labor,
      factory,
      design,
      subtotal,
      tax,
      total,
      profitMargin,
      finalPrice
    };
  }, [projectData, validateProjectData]);

  // تحديث حساب التكلفة عند تغيير البيانات
  useEffect(() => {
    if (projectData.area > 0 && projectData.selectedMaterials.length > 0 && projectData.selectedWorker &&
        projectData.selectedFactory && projectData.selectedDesigner) {
      setCalculating(true);

      // تأخير بسيط لتحسين تجربة المستخدم
      const timer = setTimeout(() => {
        const breakdown = calculateCost();
        setCostBreakdown(breakdown);
        setCalculating(false);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setCostBreakdown(null);
    }
  }, [projectData, calculateCost]);

  // حفظ المشروع
  const saveProject = async () => {
    if (!validateProjectData() || !costBreakdown) return;

    try {
      setSaving(true);

      const project: Omit<Project, 'id'> = {
        customerName: projectData.customerName,
        customerPhone: projectData.phone,
        area: projectData.area,
        furnitureType: projectData.furnitureType,
        selectedMaterial: projectData.selectedMaterials.length > 0 ?
          materials.find(m => m.id === projectData.selectedMaterials[0].materialId) || null : null,
        selectedWorker: projectData.selectedWorker!,
        selectedFactory: projectData.selectedFactory!,
        selectedDesigner: projectData.selectedDesigner!,
        totalCost: costBreakdown.finalPrice,
        breakdown: {
          materialCost: costBreakdown.materials,
          workerCost: costBreakdown.labor,
          factoryCost: costBreakdown.factory,
          designerCost: costBreakdown.design,
        },
        paidAmount: 0,
        remainingAmount: costBreakdown.finalPrice,
        status: 'قيد التنفيذ',
        invoiceStatus: 'مبدئية',
        createdAt: new Date().toISOString(),
        notes: projectData.notes
      };

      await addProject(project);
      
      handleSuccess('تم حفظ المشروع بنجاح');
      
      // إعادة تعيين النموذج
      setProjectData({
        customerName: '',
        phone: '',
        area: 0,
        furnitureType: '',
        selectedMaterials: [],
        materialsSummary: null,
        selectedWorker: null,
        selectedFactory: null,
        selectedDesigner: null,
        notes: '',
        taxRate: 0.1,
        profitMarginRate: 0.2
      });
      setCostBreakdown(null);
      
      // تحديث الإحصائيات
      if (onStatsUpdate) {
        onStatsUpdate();
      }
      
    } catch (error) {
      handleError(error, 'حفظ المشروع');
    } finally {
      setSaving(false);
    }
  };

  // تحديث بيانات المشروع
  const updateProjectData = useCallback((field: keyof ProjectData, value: any) => {
    setProjectData(prev => ({
      ...prev,
      [field]: value
    }));

    // مسح الخطأ عند التحديث
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  // معالجة تغيير المواد
  const handleMaterialsChange = useCallback((summary: ProjectMaterialsSummary) => {
    setProjectData(prev => ({
      ...prev,
      materialsSummary: summary,
      selectedMaterials: summary.materials
    }));

    // مسح خطأ المواد إن وجد
    if (errors.selectedMaterials) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.selectedMaterials;
        return newErrors;
      });
    }
  }, [errors]);

  // خيارات النماذج
  const workerOptions = useMemo(() =>
    workers.map(w => ({ value: w.id, label: `${w.name} - ${formatCurrency(w.pricePerSqm)}/م²` })),
    [workers]
  );

  const factoryOptions = useMemo(() => 
    factories.map(f => ({ value: f.id, label: `${f.name} - ${formatCurrency(f.pricePerSqm)}/م²` })),
    [factories]
  );

  const designerOptions = useMemo(() => 
    designers.map(d => ({ value: d.id, label: `${d.name} - ${formatCurrency(d.pricePerSqm)}/م²` })),
    [designers]
  );

  const furnitureTypeOptions = [
    { value: 'غرفة نوم', label: 'غرفة نوم' },
    { value: 'غرفة جلوس', label: 'غرفة جلوس' },
    { value: 'مطبخ', label: 'مطبخ' },
    { value: 'مكتب', label: 'مكتب' },
    { value: 'أخرى', label: 'أخرى' }
  ];

  if (loading) {
    return <LoadingState message="جاري تحميل بيانات الحاسبة..." />;
  }

  return (
    <div className="space-y-6">
      {/* رأس الحاسبة */}
      <div className={themeClasses.flexBetween}>
        <div className={themeClasses.flexStart}>
          <Calculator className="h-6 w-6 text-blue-600 mr-2" />
          <h2 className={themeClasses.heading3}>حاسبة التكلفة المحسنة</h2>
        </div>
        
        {costBreakdown && (
          <Badge variant="secondary" className="text-lg px-4 py-2">
            <DollarSign className="h-4 w-4 mr-1" />
            {formatCurrency(costBreakdown.finalPrice)}
          </Badge>
        )}
      </div>

      <Tabs defaultValue="project-info" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="project-info">معلومات المشروع</TabsTrigger>
          <TabsTrigger value="materials">المواد والموارد</TabsTrigger>
          <TabsTrigger value="settings">الإعدادات</TabsTrigger>
          <TabsTrigger value="results">النتائج</TabsTrigger>
        </TabsList>

        {/* معلومات المشروع */}
        <TabsContent value="project-info" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                معلومات العميل والمشروع
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className={themeClasses.gridCols2}>
                <FormField
                  type="text"
                  name="customerName"
                  label="اسم العميل"
                  value={projectData.customerName}
                  onChange={(value) => updateProjectData('customerName', value)}
                  error={errors.customerName}
                  required
                  placeholder="أدخل اسم العميل"
                />
                
                <FormField
                  type="tel"
                  name="phone"
                  label="رقم الهاتف"
                  value={projectData.phone}
                  onChange={(value) => updateProjectData('phone', value)}
                  error={errors.phone}
                  required
                  placeholder="أدخل رقم الهاتف"
                />
              </div>

              <div className={themeClasses.gridCols2}>
                <FormField
                  type="number"
                  name="area"
                  label="المساحة (متر مربع)"
                  value={projectData.area}
                  onChange={(value) => updateProjectData('area', value)}
                  error={errors.area}
                  required
                  min={0.1}
                  step={0.1}
                  placeholder="أدخل المساحة"
                />
                
                <FormField
                  type="select"
                  name="furnitureType"
                  label="نوع الأثاث"
                  value={projectData.furnitureType}
                  onChange={(value) => updateProjectData('furnitureType', value)}
                  options={furnitureTypeOptions}
                  error={errors.furnitureType}
                  required
                  placeholder="اختر نوع الأثاث"
                />
              </div>

              <FormField
                type="textarea"
                name="notes"
                label="ملاحظات إضافية"
                value={projectData.notes}
                onChange={(value) => updateProjectData('notes', value)}
                placeholder="أدخل أي ملاحظات إضافية..."
                rows={3}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* المواد والموارد */}
        <TabsContent value="materials" className="space-y-6">
          {/* اختيار المواد المحسن */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                اختيار المواد
              </CardTitle>
            </CardHeader>
            <CardContent>
              <MaterialSelector
                onMaterialsChange={handleMaterialsChange}
                initialMaterials={projectData.selectedMaterials}
              />
              {errors.selectedMaterials && (
                <p className="text-red-500 text-sm mt-2">{errors.selectedMaterials}</p>
              )}
            </CardContent>
          </Card>

          {/* عرض المواد المختارة */}
          {projectData.selectedMaterials.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  المواد المختارة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SelectedMaterialsTable
                  materials={projectData.selectedMaterials}
                  onMaterialsChange={handleMaterialsChange}
                />
              </CardContent>
            </Card>
          )}

          <div className={themeClasses.gridCols3}>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  اختيار العامل
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  type="select"
                  name="selectedWorker"
                  value={projectData.selectedWorker?.id || ''}
                  onChange={(value) => {
                    const worker = workers.find(w => w.id === value);
                    updateProjectData('selectedWorker', worker || null);
                  }}
                  options={workerOptions}
                  error={errors.selectedWorker}
                  required
                  placeholder="اختر العامل"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FactoryIcon className="h-5 w-5" />
                  اختيار المصنع
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  type="select"
                  name="selectedFactory"
                  value={projectData.selectedFactory?.id || ''}
                  onChange={(value) => {
                    const factory = factories.find(f => f.id === value);
                    updateProjectData('selectedFactory', factory || null);
                  }}
                  options={factoryOptions}
                  error={errors.selectedFactory}
                  required
                  placeholder="اختر المصنع"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  اختيار المصمم
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  type="select"
                  name="selectedDesigner"
                  value={projectData.selectedDesigner?.id || ''}
                  onChange={(value) => {
                    const designer = designers.find(d => d.id === value);
                    updateProjectData('selectedDesigner', designer || null);
                  }}
                  options={designerOptions}
                  error={errors.selectedDesigner}
                  required
                  placeholder="اختر المصمم"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* الإعدادات */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>إعدادات الحساب</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className={themeClasses.gridCols2}>
                <FormField
                  type="number"
                  name="taxRate"
                  label="معدل الضريبة (%)"
                  value={projectData.taxRate * 100}
                  onChange={(value) => updateProjectData('taxRate', value / 100)}
                  min={0}
                  max={100}
                  step={0.1}
                  placeholder="أدخل معدل الضريبة"
                />
                
                <FormField
                  type="number"
                  name="profitMarginRate"
                  label="هامش الربح (%)"
                  value={projectData.profitMarginRate * 100}
                  onChange={(value) => updateProjectData('profitMarginRate', value / 100)}
                  min={0}
                  max={100}
                  step={0.1}
                  placeholder="أدخل هامش الربح"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* النتائج */}
        <TabsContent value="results" className="space-y-6">
          <DataState
            loading={calculating}
            empty={!costBreakdown}
            emptyTitle="لا توجد نتائج حتى الآن"
            emptyDescription="أكمل إدخال بيانات المشروع لرؤية النتائج"
          >
            {costBreakdown && (
              <div className="space-y-6">
                {/* تفصيل التكاليف */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      تفصيل التكاليف
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className={themeClasses.flexBetween}>
                        <span>تكلفة المواد:</span>
                        <span className="font-semibold">{formatCurrency(costBreakdown.materials)}</span>
                      </div>
                      <div className={themeClasses.flexBetween}>
                        <span>تكلفة العمالة:</span>
                        <span className="font-semibold">{formatCurrency(costBreakdown.labor)}</span>
                      </div>
                      <div className={themeClasses.flexBetween}>
                        <span>تكلفة المصنع:</span>
                        <span className="font-semibold">{formatCurrency(costBreakdown.factory)}</span>
                      </div>
                      <div className={themeClasses.flexBetween}>
                        <span>تكلفة التصميم:</span>
                        <span className="font-semibold">{formatCurrency(costBreakdown.design)}</span>
                      </div>
                      <hr />
                      <div className={themeClasses.flexBetween}>
                        <span>المجموع الفرعي:</span>
                        <span className="font-semibold">{formatCurrency(costBreakdown.subtotal)}</span>
                      </div>
                      <div className={themeClasses.flexBetween}>
                        <span>الضريبة ({(projectData.taxRate * 100).toFixed(1)}%):</span>
                        <span className="font-semibold">{formatCurrency(costBreakdown.tax)}</span>
                      </div>
                      <div className={themeClasses.flexBetween}>
                        <span>هامش الربح ({(projectData.profitMarginRate * 100).toFixed(1)}%):</span>
                        <span className="font-semibold">{formatCurrency(costBreakdown.profitMargin)}</span>
                      </div>
                      <hr className="border-2" />
                      <div className={cn(themeClasses.flexBetween, 'text-lg font-bold text-green-600')}>
                        <span>السعر النهائي:</span>
                        <span>{formatCurrency(costBreakdown.finalPrice)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* أزرار الإجراءات */}
                <div className={themeClasses.flexCenter}>
                  <Button
                    onClick={saveProject}
                    disabled={saving}
                    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                    size="lg"
                  >
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        حفظ المشروع
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </DataState>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedCostCalculator;
