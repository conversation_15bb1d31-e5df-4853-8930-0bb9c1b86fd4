import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  DollarSign, 
  Calendar, 
  Receipt, 
  Plus,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Clock
} from 'lucide-react';
import { formatLibyanDinar } from '@/utils/calculations';
import { useToast } from '@/hooks/use-toast';
import { 
  getProjects, 
  updateProject, 
  addCashTransaction,
  getCashTransactions,
  Project,
  CashTransaction 
} from '@/utils/dataManager';
import { useAppContext } from '@/contexts/AppContext';

interface Payment {
  id: string;
  projectId: string;
  customerName: string;
  amount: number;
  paymentDate: string;
  paymentMethod: 'نقدي' | 'تحويل بنكي' | 'شيك';
  description: string;
  notes?: string;
  createdAt: string;
}

interface PaymentManagerProps {
  projectId?: string;
  onPaymentAdded?: () => void;
}

const PaymentManager: React.FC<PaymentManagerProps> = ({ projectId, onPaymentAdded }) => {
  const { toast } = useToast();
  const { state, dispatch } = useAppContext();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isAddPaymentOpen, setIsAddPaymentOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // نموذج الدفعة الجديدة
  const [paymentForm, setPaymentForm] = useState({
    projectId: projectId || '',
    amount: '',
    paymentMethod: 'نقدي' as 'نقدي' | 'تحويل بنكي' | 'شيك',
    description: '',
    notes: '',
    paymentDate: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    loadData();
  }, [projectId]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // تحميل المشاريع إذا لم تكن محملة
      if (state.projects.length === 0) {
        const projectsData = await getProjects();
        dispatch({ type: 'SET_PROJECTS', payload: projectsData });
      }

      // تحميل المعاملات المالية
      const transactionsData = await getCashTransactions();
      dispatch({ type: 'SET_TRANSACTIONS', payload: transactionsData });

      // تصفية الدفعات من المعاملات
      const paymentTransactions = transactionsData.filter(
        (t: CashTransaction) => t.type === 'دخل' && t.category === 'مشروع'
      );

      const paymentsData: Payment[] = paymentTransactions.map((t: CashTransaction) => ({
        id: t.id,
        projectId: t.relatedId || '',
        customerName: t.relatedName || '',
        amount: t.amount,
        paymentDate: t.date,
        paymentMethod: t.paymentMethod || 'نقدي',
        description: t.description,
        notes: t.notes,
        createdAt: t.createdAt || t.date
      }));

      setPayments(projectId ? paymentsData.filter(p => p.projectId === projectId) : paymentsData);

      // تحديد المشروع المحدد
      if (projectId) {
        const project = state.projects.find(p => p.id === projectId);
        setSelectedProject(project || null);
      }

    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات الدفعات",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddPayment = async () => {
    try {
      // التحقق من صحة البيانات
      if (!paymentForm.projectId || !paymentForm.amount || parseFloat(paymentForm.amount) <= 0) {
        toast({
          title: "بيانات غير صحيحة",
          description: "يرجى التأكد من اختيار المشروع وإدخال مبلغ صحيح",
          variant: "destructive",
        });
        return;
      }

      const project = state.projects.find(p => p.id === paymentForm.projectId);
      if (!project) {
        toast({
          title: "مشروع غير موجود",
          description: "المشروع المحدد غير موجود",
          variant: "destructive",
        });
        return;
      }

      const paymentAmount = parseFloat(paymentForm.amount);
      
      // التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
      if (paymentAmount > project.remainingAmount) {
        toast({
          title: "مبلغ غير صحيح",
          description: `المبلغ المدخل أكبر من المبلغ المتبقي (${formatLibyanDinar(project.remainingAmount)})`,
          variant: "destructive",
        });
        return;
      }

      setLoading(true);

      // إنشاء معاملة مالية
      const transaction: Omit<CashTransaction, 'id'> = {
        type: 'دخل',
        category: 'مشروع',
        amount: paymentAmount,
        description: paymentForm.description || `دفعة من ${project.customerName}`,
        relatedId: project.id,
        relatedName: project.customerName,
        date: paymentForm.paymentDate,
        paymentMethod: paymentForm.paymentMethod,
        notes: paymentForm.notes,
        createdAt: new Date().toISOString()
      };

      const transactionId = await addCashTransaction(transaction);
      
      if (transactionId) {
        // تحديث المشروع
        const newPaidAmount = project.paidAmount + paymentAmount;
        const newRemainingAmount = project.totalCost - newPaidAmount;
        
        const success = await updateProject(project.id, {
          paidAmount: newPaidAmount,
          remainingAmount: newRemainingAmount
        });

        if (success) {
          // تحديث الحالة المحلية
          dispatch({
            type: 'UPDATE_PROJECT',
            payload: {
              id: project.id,
              updates: {
                paidAmount: newPaidAmount,
                remainingAmount: newRemainingAmount
              }
            }
          });

          // إضافة المعاملة للحالة
          dispatch({
            type: 'ADD_TRANSACTION',
            payload: { ...transaction, id: transactionId }
          });

          // إعادة تحميل البيانات
          await loadData();

          // إعادة تعيين النموذج
          setPaymentForm({
            projectId: projectId || '',
            amount: '',
            paymentMethod: 'نقدي',
            description: '',
            notes: '',
            paymentDate: new Date().toISOString().split('T')[0]
          });

          setIsAddPaymentOpen(false);

          toast({
            title: "تم تسجيل الدفعة",
            description: `تم تسجيل دفعة بقيمة ${formatLibyanDinar(paymentAmount)} بنجاح`,
          });

          onPaymentAdded?.();
        }
      }

    } catch (error) {
      console.error('خطأ في إضافة الدفعة:', error);
      toast({
        title: "خطأ في التسجيل",
        description: "حدث خطأ أثناء تسجيل الدفعة",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getPaymentStatusBadge = (project: Project) => {
    const percentage = (project.paidAmount / project.totalCost) * 100;
    
    if (percentage === 100) {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />مكتمل</Badge>;
    } else if (percentage >= 50) {
      return <Badge className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />جزئي</Badge>;
    } else if (percentage > 0) {
      return <Badge className="bg-yellow-100 text-yellow-800"><AlertTriangle className="h-3 w-3 mr-1" />بدء</Badge>;
    } else {
      return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />لم يدفع</Badge>;
    }
  };

  const availableProjects = state.projects.filter(p => p.remainingAmount > 0);

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              إدارة الدفعات {selectedProject && `- ${selectedProject.customerName}`}
            </CardTitle>
            
            <Dialog open={isAddPaymentOpen} onOpenChange={setIsAddPaymentOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  إضافة دفعة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>إضافة دفعة جديدة</DialogTitle>
                </DialogHeader>
                
                <div className="space-y-4">
                  {!projectId && (
                    <div className="space-y-2">
                      <Label>المشروع *</Label>
                      <Select 
                        value={paymentForm.projectId} 
                        onValueChange={(value) => setPaymentForm(prev => ({ ...prev, projectId: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر المشروع" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableProjects.map((project) => (
                            <SelectItem key={project.id} value={project.id}>
                              {project.customerName} - {formatLibyanDinar(project.remainingAmount)} متبقي
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label>المبلغ (د.ل) *</Label>
                    <Input
                      type="number"
                      placeholder="أدخل المبلغ"
                      value={paymentForm.amount}
                      onChange={(e) => setPaymentForm(prev => ({ ...prev, amount: e.target.value }))}
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>طريقة الدفع *</Label>
                    <Select 
                      value={paymentForm.paymentMethod} 
                      onValueChange={(value: 'نقدي' | 'تحويل بنكي' | 'شيك') => 
                        setPaymentForm(prev => ({ ...prev, paymentMethod: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="نقدي">نقدي</SelectItem>
                        <SelectItem value="تحويل بنكي">تحويل بنكي</SelectItem>
                        <SelectItem value="شيك">شيك</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>تاريخ الدفعة *</Label>
                    <Input
                      type="date"
                      value={paymentForm.paymentDate}
                      onChange={(e) => setPaymentForm(prev => ({ ...prev, paymentDate: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>الوصف</Label>
                    <Input
                      placeholder="وصف الدفعة"
                      value={paymentForm.description}
                      onChange={(e) => setPaymentForm(prev => ({ ...prev, description: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>ملاحظات</Label>
                    <Textarea
                      placeholder="ملاحظات إضافية"
                      value={paymentForm.notes}
                      onChange={(e) => setPaymentForm(prev => ({ ...prev, notes: e.target.value }))}
                      rows={3}
                    />
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button 
                      onClick={handleAddPayment} 
                      disabled={loading}
                      className="flex-1"
                    >
                      {loading ? 'جاري التسجيل...' : 'تسجيل الدفعة'}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsAddPaymentOpen(false)}
                      disabled={loading}
                    >
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      {/* قائمة الدفعات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            سجل الدفعات ({payments.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل الدفعات...</p>
            </div>
          ) : payments.length === 0 ? (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">لا توجد دفعات مسجلة</p>
            </div>
          ) : (
            <div className="space-y-4">
              {payments.map((payment) => {
                const project = state.projects.find(p => p.id === payment.projectId);
                return (
                  <Card key={payment.id} className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold">{payment.customerName}</h4>
                            <Badge variant="outline">{payment.paymentMethod}</Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600">المبلغ</p>
                              <p className="font-medium text-green-600">{formatLibyanDinar(payment.amount)}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">التاريخ</p>
                              <p className="font-medium">{new Date(payment.paymentDate).toLocaleDateString('ar-LY')}</p>
                            </div>
                            <div>
                              <p className="text-gray-600">الوصف</p>
                              <p className="font-medium">{payment.description}</p>
                            </div>
                            {project && (
                              <div>
                                <p className="text-gray-600">حالة المشروع</p>
                                {getPaymentStatusBadge(project)}
                              </div>
                            )}
                          </div>

                          {payment.notes && (
                            <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                              <p className="text-gray-600">ملاحظات: {payment.notes}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentManager;
