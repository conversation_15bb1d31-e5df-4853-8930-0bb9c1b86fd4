// مكون حقل النموذج الموحد
import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Eye, EyeOff } from 'lucide-react';
import { themeClasses } from '@/styles/theme';
import { cn } from '@/lib/utils';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface BaseFieldProps {
  label?: string;
  name: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  description?: string;
}

interface TextFieldProps extends BaseFieldProps {
  type: 'text' | 'email' | 'tel' | 'url';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
}

interface NumberFieldProps extends BaseFieldProps {
  type: 'number';
  value: number;
  onChange: (value: number) => void;
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
}

interface PasswordFieldProps extends BaseFieldProps {
  type: 'password';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  showToggle?: boolean;
}

interface TextareaFieldProps extends BaseFieldProps {
  type: 'textarea';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  maxLength?: number;
}

interface SelectFieldProps extends BaseFieldProps {
  type: 'select';
  value: string;
  onChange: (value: string) => void;
  options: SelectOption[];
  placeholder?: string;
}

interface CheckboxFieldProps extends BaseFieldProps {
  type: 'checkbox';
  checked: boolean;
  onChange: (checked: boolean) => void;
}

interface FileFieldProps extends BaseFieldProps {
  type: 'file';
  onChange: (files: FileList | null) => void;
  accept?: string;
  multiple?: boolean;
}

type FormFieldProps = 
  | TextFieldProps 
  | NumberFieldProps 
  | PasswordFieldProps 
  | TextareaFieldProps 
  | SelectFieldProps 
  | CheckboxFieldProps 
  | FileFieldProps;

const FormField: React.FC<FormFieldProps> = (props) => {
  const [showPassword, setShowPassword] = React.useState(false);

  const renderLabel = () => {
    if (!props.label) return null;

    return (
      <Label 
        htmlFor={props.name}
        className={cn(
          'text-sm font-medium text-gray-700 mb-2 block',
          props.required && 'after:content-["*"] after:text-red-500 after:ml-1'
        )}
      >
        {props.label}
      </Label>
    );
  };

  const renderDescription = () => {
    if (!props.description) return null;

    return (
      <p className={cn(themeClasses.smallText, 'mt-1')}>
        {props.description}
      </p>
    );
  };

  const renderError = () => {
    if (!props.error) return null;

    return (
      <div className="flex items-center gap-1 mt-1 text-red-600">
        <AlertCircle className="h-4 w-4" />
        <span className="text-sm">{props.error}</span>
      </div>
    );
  };

  const renderField = () => {
    const baseClasses = cn(
      themeClasses.transition,
      props.error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
      props.disabled && 'opacity-50 cursor-not-allowed'
    );

    switch (props.type) {
      case 'text':
      case 'email':
      case 'tel':
      case 'url':
        return (
          <Input
            id={props.name}
            name={props.name}
            type={props.type}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            placeholder={props.placeholder}
            disabled={props.disabled}
            maxLength={props.maxLength}
            minLength={props.minLength}
            className={baseClasses}
          />
        );

      case 'number':
        return (
          <Input
            id={props.name}
            name={props.name}
            type="number"
            value={props.value}
            onChange={(e) => props.onChange(Number(e.target.value))}
            placeholder={props.placeholder}
            disabled={props.disabled}
            min={props.min}
            max={props.max}
            step={props.step}
            className={baseClasses}
          />
        );

      case 'password':
        return (
          <div className="relative">
            <Input
              id={props.name}
              name={props.name}
              type={showPassword ? 'text' : 'password'}
              value={props.value}
              onChange={(e) => props.onChange(e.target.value)}
              placeholder={props.placeholder}
              disabled={props.disabled}
              className={cn(baseClasses, props.showToggle && 'pr-10')}
            />
            {props.showToggle && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        );

      case 'textarea':
        return (
          <Textarea
            id={props.name}
            name={props.name}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            placeholder={props.placeholder}
            disabled={props.disabled}
            rows={props.rows || 3}
            maxLength={props.maxLength}
            className={baseClasses}
          />
        );

      case 'select':
        return (
          <Select
            value={props.value}
            onValueChange={props.onChange}
            disabled={props.disabled}
          >
            <SelectTrigger className={baseClasses}>
              <SelectValue placeholder={props.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {props.options.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2 space-x-reverse">
            <input
              id={props.name}
              name={props.name}
              type="checkbox"
              checked={props.checked}
              onChange={(e) => props.onChange(e.target.checked)}
              disabled={props.disabled}
              className={cn(
                'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded',
                themeClasses.transition,
                props.disabled && 'opacity-50 cursor-not-allowed'
              )}
            />
            {props.label && (
              <Label
                htmlFor={props.name}
                className={cn(
                  'text-sm font-medium text-gray-700 cursor-pointer',
                  props.disabled && 'opacity-50 cursor-not-allowed'
                )}
              >
                {props.label}
              </Label>
            )}
          </div>
        );

      case 'file':
        return (
          <Input
            id={props.name}
            name={props.name}
            type="file"
            onChange={(e) => props.onChange(e.target.files)}
            accept={props.accept}
            multiple={props.multiple}
            disabled={props.disabled}
            className={baseClasses}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn('space-y-2', props.className)}>
      {props.type !== 'checkbox' && renderLabel()}
      {renderField()}
      {renderDescription()}
      {renderError()}
    </div>
  );
};

export default FormField;
