import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Search, 
  Plus, 
  Minus, 
  Calculator,
  Package,
  AlertTriangle
} from 'lucide-react';
import { DetailedMaterial, MaterialCalculation } from '@/types/materials';
import { formatLibyanDinar } from '@/utils/calculations';

interface MaterialsTableProps {
  materials: DetailedMaterial[];
  selectedMaterials: MaterialCalculation[];
  onMaterialSelect: (material: DetailedMaterial, quantity: number) => void;
  onMaterialRemove: (materialId: string) => void;
  onQuantityChange: (materialId: string, quantity: number) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  categoryFilter?: string;
}

const MaterialsTable: React.FC<MaterialsTableProps> = ({
  materials,
  selectedMaterials,
  onMaterialSelect,
  onMaterialRemove,
  onQuantityChange,
  searchQuery,
  onSearchChange,
  categoryFilter
}) => {
  const [filteredMaterials, setFilteredMaterials] = useState<DetailedMaterial[]>([]);

  useEffect(() => {
    let filtered = materials.filter(material => material.isActive);

    // فلترة حسب البحث
    if (searchQuery) {
      filtered = filtered.filter(material =>
        material.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        material.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        material.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // فلترة حسب الفئة
    if (categoryFilter && categoryFilter !== 'all') {
      filtered = filtered.filter(material => material.category.id === categoryFilter);
    }

    setFilteredMaterials(filtered);
  }, [materials, searchQuery, categoryFilter]);

  const isSelected = (materialId: string) => {
    return selectedMaterials.some(sm => sm.materialId === materialId);
  };

  const getSelectedQuantity = (materialId: string) => {
    const selected = selectedMaterials.find(sm => sm.materialId === materialId);
    return selected ? selected.requiredQuantity : 0;
  };

  const handleQuantityInput = (material: DetailedMaterial, quantity: number) => {
    if (quantity > 0) {
      onMaterialSelect(material, quantity);
    } else {
      onMaterialRemove(material.id);
    }
  };

  const incrementQuantity = (material: DetailedMaterial) => {
    const currentQuantity = getSelectedQuantity(material.id);
    handleQuantityInput(material, currentQuantity + 1);
  };

  const decrementQuantity = (material: DetailedMaterial) => {
    const currentQuantity = getSelectedQuantity(material.id);
    if (currentQuantity > 0) {
      handleQuantityInput(material, currentQuantity - 1);
    }
  };

  const getCategoryColor = (categoryColor: string) => {
    return {
      backgroundColor: categoryColor + '20',
      borderColor: categoryColor,
      color: categoryColor
    };
  };

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          جدول المواد والخامات
        </CardTitle>
        
        {/* شريط البحث */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="البحث في المواد (الاسم، الرقم، الوصف...)"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>

      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">رقم المادة</TableHead>
                <TableHead className="text-right">اسم المادة</TableHead>
                <TableHead className="text-right">الفئة</TableHead>
                <TableHead className="text-right">الوحدة</TableHead>
                <TableHead className="text-right">سعر الشراء</TableHead>
                <TableHead className="text-right">سعر البيع</TableHead>
                <TableHead className="text-right">المخزن</TableHead>
                <TableHead className="text-right">الكمية المطلوبة</TableHead>
                <TableHead className="text-right">إجمالي الشراء</TableHead>
                <TableHead className="text-right">إجمالي البيع</TableHead>
                <TableHead className="text-right">المكسب</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMaterials.map((material) => {
                const selectedQuantity = getSelectedQuantity(material.id);
                const totalPurchase = selectedQuantity * material.purchasePrice;
                const totalSale = selectedQuantity * material.salePrice;
                const profit = totalSale - totalPurchase;
                const isLowStock = material.availableQuantity <= material.minQuantity;

                return (
                  <TableRow 
                    key={material.id}
                    className={`${isSelected(material.id) ? 'bg-blue-50' : ''} ${isLowStock ? 'bg-red-50' : ''}`}
                  >
                    <TableCell className="font-mono font-semibold">
                      {material.code}
                    </TableCell>
                    
                    <TableCell>
                      <div>
                        <div className="font-medium">{material.name}</div>
                        {material.description && (
                          <div className="text-sm text-gray-500">{material.description}</div>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge 
                        variant="outline"
                        style={getCategoryColor(material.category.color)}
                      >
                        {material.category.name}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>{material.unit}</TableCell>
                    
                    <TableCell className="font-semibold text-red-600">
                      {formatLibyanDinar(material.purchasePrice)}
                    </TableCell>
                    
                    <TableCell className="font-semibold text-green-600">
                      {formatLibyanDinar(material.salePrice)}
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span className={isLowStock ? 'text-red-600 font-semibold' : ''}>
                          {material.availableQuantity}
                        </span>
                        {isLowStock && <AlertTriangle className="h-4 w-4 text-red-500" />}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => decrementQuantity(material)}
                          disabled={selectedQuantity === 0}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        
                        <Input
                          type="number"
                          min="0"
                          value={selectedQuantity}
                          onChange={(e) => handleQuantityInput(material, parseFloat(e.target.value) || 0)}
                          className="w-20 text-center"
                        />
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => incrementQuantity(material)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                    
                    <TableCell className="font-semibold text-red-600">
                      {selectedQuantity > 0 ? formatLibyanDinar(totalPurchase) : '-'}
                    </TableCell>
                    
                    <TableCell className="font-semibold text-green-600">
                      {selectedQuantity > 0 ? formatLibyanDinar(totalSale) : '-'}
                    </TableCell>
                    
                    <TableCell className="font-semibold text-blue-600">
                      {selectedQuantity > 0 ? formatLibyanDinar(profit) : '-'}
                    </TableCell>
                    
                    <TableCell>
                      {isSelected(material.id) && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => onMaterialRemove(material.id)}
                        >
                          إزالة
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {filteredMaterials.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>لا توجد مواد تطابق معايير البحث</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MaterialsTable;
