// مكون شبكة الإحصائيات الموحد
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';
import { themeClasses } from '@/styles/theme';
import { cn } from '@/lib/utils';

export interface StatItem {
  id: string;
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

interface StatsGridProps {
  stats: StatItem[];
  columns?: 2 | 3 | 4;
  className?: string;
}

const StatsGrid: React.FC<StatsGridProps> = ({
  stats,
  columns = 4,
  className
}) => {
  const gridClasses = {
    2: themeClasses.gridCols2,
    3: themeClasses.gridCols3,
    4: themeClasses.gridCols4,
  };

  const colorClasses = {
    primary: {
      gradient: 'bg-gradient-to-r from-blue-600 to-blue-700',
      text: 'text-blue-600',
      bg: 'bg-blue-50',
    },
    secondary: {
      gradient: 'bg-gradient-to-r from-green-600 to-green-700',
      text: 'text-green-600',
      bg: 'bg-green-50',
    },
    success: {
      gradient: 'bg-gradient-to-r from-emerald-600 to-emerald-700',
      text: 'text-emerald-600',
      bg: 'bg-emerald-50',
    },
    warning: {
      gradient: 'bg-gradient-to-r from-yellow-600 to-orange-600',
      text: 'text-orange-600',
      bg: 'bg-orange-50',
    },
    error: {
      gradient: 'bg-gradient-to-r from-red-600 to-red-700',
      text: 'text-red-600',
      bg: 'bg-red-50',
    },
    info: {
      gradient: 'bg-gradient-to-r from-cyan-600 to-cyan-700',
      text: 'text-cyan-600',
      bg: 'bg-cyan-50',
    },
  };

  return (
    <div className={cn(gridClasses[columns], className)}>
      {stats.map((stat) => {
        const Icon = stat.icon;
        const colors = colorClasses[stat.color || 'primary'];

        return (
          <Card 
            key={stat.id} 
            className={cn(
              'shadow-lg border-0 bg-white/80 backdrop-blur-sm',
              themeClasses.transition,
              'hover:shadow-xl hover:scale-105'
            )}
          >
            <CardHeader className={cn(
              'text-white rounded-t-lg',
              colors.gradient
            )}>
              <CardTitle className="flex items-center gap-2">
                <Icon className="h-6 w-6" />
                {stat.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-2">
                {/* القيمة الرئيسية */}
                <p className={cn(
                  'text-3xl font-bold',
                  colors.text
                )}>
                  {stat.value}
                </p>

                {/* النص الفرعي */}
                {stat.subtitle && (
                  <p className={themeClasses.smallText}>
                    {stat.subtitle}
                  </p>
                )}

                {/* مؤشر الاتجاه */}
                {stat.trend && (
                  <div className={cn(
                    'flex items-center gap-1 text-sm',
                    stat.trend.isPositive ? 'text-green-600' : 'text-red-600'
                  )}>
                    <span className={cn(
                      'inline-block w-0 h-0 border-l-4 border-r-4 border-transparent',
                      stat.trend.isPositive 
                        ? 'border-b-4 border-b-green-600' 
                        : 'border-t-4 border-t-red-600'
                    )} />
                    {Math.abs(stat.trend.value)}%
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default StatsGrid;
