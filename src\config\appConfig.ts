// تكوين التطبيق الموحد
// يحتوي على جميع الإعدادات والثوابت المستخدمة في التطبيق

export const APP_CONFIG = {
  // معلومات التطبيق
  name: 'نظام إدارة مصنع الأثاث',
  version: '1.0.0',
  description: 'نظام شامل لإدارة مصنع الأثاث والتكاليف',
  
  // إعدادات قاعدة البيانات
  database: {
    name: 'furniture_factory.db',
    version: 1,
    timeout: 30000, // 30 ثانية
  },
  
  // إعدادات واجهة المستخدم
  ui: {
    theme: 'light',
    language: 'ar',
    direction: 'rtl',
    pageSize: 10,
    maxItemsPerPage: 50,
  },
  
  // إعدادات العملة
  currency: {
    symbol: 'د.ل',
    name: 'دينار ليبي',
    code: 'LYD',
    decimals: 3,
  },
  
  // إعدادات التحقق من البيانات
  validation: {
    minNameLength: 2,
    maxNameLength: 100,
    minPhoneLength: 9,
    maxPhoneLength: 15,
    minArea: 0.1,
    maxArea: 10000,
    minPrice: 0.001,
    maxPrice: 1000000,
  },
  
  // حالات المشاريع المتاحة
  projectStatuses: [
    'جديد',
    'قيد التنفيذ',
    'متأخر',
    'مكتمل',
    'ملغي',
    'معلق'
  ],
  
  // حالات الفواتير
  invoiceStatuses: [
    'مبدئية',
    'تصنيع',
    'مكتملة',
    'ملغية'
  ],
  
  // أنواع المعاملات المالية
  transactionTypes: [
    'دفع مشروع',
    'راتب موظف',
    'شراء مواد',
    'مصروف عام',
    'إيراد إضافي'
  ],
  
  // فئات المواد الافتراضية
  materialCategories: [
    'أخشاب',
    'معادن',
    'أقمشة',
    'زجاج',
    'مواد لاصقة',
    'دهانات',
    'إكسسوارات',
    'أدوات'
  ],
  
  // تخصصات العمال
  workerSpecialties: [
    'نجار',
    'حداد',
    'منجد',
    'دهان',
    'مصمم',
    'مساعد',
    'فني تركيب'
  ],
  
  // أنواع الأثاث
  furnitureTypes: [
    'غرفة نوم',
    'غرفة جلوس',
    'مطبخ',
    'مكتب',
    'خزانة',
    'طاولة',
    'كرسي',
    'سرير',
    'أخرى'
  ],
  
  // إعدادات التنبيهات
  notifications: {
    maxCount: 50,
    autoDeleteAfterDays: 30,
    lowStockThreshold: 10,
    overdueProjectDays: 7,
  },
  
  // إعدادات الطباعة
  printing: {
    pageSize: 'A4',
    orientation: 'portrait',
    margins: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20
    },
    fontSize: {
      title: 18,
      header: 14,
      body: 12,
      footer: 10
    }
  },
  
  // إعدادات التصدير
  export: {
    formats: ['pdf', 'excel', 'csv'],
    maxRecords: 10000,
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss'
  },
  
  // رسائل الخطأ الافتراضية
  errorMessages: {
    networkError: 'خطأ في الاتصال بالشبكة',
    databaseError: 'خطأ في قاعدة البيانات',
    validationError: 'بيانات غير صحيحة',
    permissionError: 'ليس لديك صلاحية للقيام بهذا الإجراء',
    notFoundError: 'العنصر المطلوب غير موجود',
    duplicateError: 'هذا العنصر موجود بالفعل',
    unknownError: 'حدث خطأ غير متوقع'
  },
  
  // رسائل النجاح
  successMessages: {
    created: 'تم الإنشاء بنجاح',
    updated: 'تم التحديث بنجاح',
    deleted: 'تم الحذف بنجاح',
    saved: 'تم الحفظ بنجاح',
    exported: 'تم التصدير بنجاح',
    imported: 'تم الاستيراد بنجاح',
    printed: 'تم الطباعة بنجاح'
  },
  
  // إعدادات الأداء
  performance: {
    debounceDelay: 300, // مللي ثانية
    cacheTimeout: 300000, // 5 دقائق
    maxCacheSize: 100,
    lazyLoadThreshold: 20
  },
  
  // إعدادات الأمان
  security: {
    sessionTimeout: 3600000, // ساعة واحدة
    maxLoginAttempts: 5,
    passwordMinLength: 6,
    encryptionKey: 'furniture-factory-2024'
  },
  
  // مسارات الملفات
  paths: {
    database: './data/',
    exports: './exports/',
    imports: './imports/',
    backups: './backups/',
    logs: './logs/'
  },
  
  // إعدادات السجلات
  logging: {
    level: 'info', // debug, info, warn, error
    maxFileSize: 10485760, // 10MB
    maxFiles: 5,
    datePattern: 'YYYY-MM-DD'
  }
};

// دوال مساعدة للوصول للإعدادات
export const getConfig = (path: string) => {
  return path.split('.').reduce((obj, key) => obj?.[key], APP_CONFIG);
};

export const getCurrencySymbol = () => APP_CONFIG.currency.symbol;
export const getCurrencyCode = () => APP_CONFIG.currency.code;
export const getAppName = () => APP_CONFIG.name;
export const getAppVersion = () => APP_CONFIG.version;

// التحقق من صحة الإعدادات
export const validateConfig = () => {
  const errors: string[] = [];
  
  if (!APP_CONFIG.name || APP_CONFIG.name.trim().length === 0) {
    errors.push('اسم التطبيق مطلوب');
  }
  
  if (!APP_CONFIG.version || APP_CONFIG.version.trim().length === 0) {
    errors.push('إصدار التطبيق مطلوب');
  }
  
  if (APP_CONFIG.validation.minArea >= APP_CONFIG.validation.maxArea) {
    errors.push('الحد الأدنى للمساحة يجب أن يكون أقل من الحد الأقصى');
  }
  
  if (APP_CONFIG.validation.minPrice >= APP_CONFIG.validation.maxPrice) {
    errors.push('الحد الأدنى للسعر يجب أن يكون أقل من الحد الأقصى');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// تحديث الإعدادات
export const updateConfig = (path: string, value: any) => {
  const keys = path.split('.');
  let current = APP_CONFIG as any;
  
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }
  
  current[keys[keys.length - 1]] = value;
};

// إعادة تعيين الإعدادات للقيم الافتراضية
export const resetConfig = () => {
  // يمكن تنفيذ هذا إذا كان هناك حاجة لإعادة تعيين الإعدادات
  console.log('إعادة تعيين الإعدادات للقيم الافتراضية');
};

// تصدير الإعدادات كـ JSON
export const exportConfig = () => {
  return JSON.stringify(APP_CONFIG, null, 2);
};

// استيراد الإعدادات من JSON
export const importConfig = (configJson: string) => {
  try {
    const importedConfig = JSON.parse(configJson);
    Object.assign(APP_CONFIG, importedConfig);
    return true;
  } catch (error) {
    console.error('خطأ في استيراد الإعدادات:', error);
    return false;
  }
};
