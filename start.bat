@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    نظام إدارة مصنع الأثاث
echo    تطبيق سطح المكتب
echo ========================================
echo.

echo 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    echo 📥 تحميل من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js مثبت
echo.

echo 📦 فحص التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
) else (
    echo ✅ التبعيات مثبتة
)

echo.
echo 🚀 تشغيل التطبيق...
echo.
npm run electron:dev

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo 🔧 جرب الأوامر التالية:
    echo    npm run clean
    echo    npm install
    echo    npm run electron:dev
    pause
)
