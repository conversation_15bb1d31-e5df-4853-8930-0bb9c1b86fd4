const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { app } = require('electron');

let db = null;

// تحديد مسار قاعدة البيانات
const getDBPath = () => {
  try {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'furniture_factory.db');
  } catch (error) {
    return path.join(__dirname, '..', 'furniture_factory.db');
  }
};

// إنشاء اتصال قاعدة البيانات
const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    const dbPath = getDBPath();
    console.log('مسار قاعدة البيانات:', dbPath);
    
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
        reject(err);
      } else {
        console.log('تم الاتصال بقاعدة البيانات بنجاح');
        createTables().then(resolve).catch(reject);
      }
    });
  });
};

// إنشاء الجداول
const createTables = () => {
  return new Promise((resolve, reject) => {
    const tables = [
      // جدول المواد (الأصلي - للتوافق مع النظام القديم)
      `CREATE TABLE IF NOT EXISTS materials (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        pricePerSqm REAL NOT NULL,
        category TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول فئات المواد
      `CREATE TABLE IF NOT EXISTS material_categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        color TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول المواد التفصيلية
      `CREATE TABLE IF NOT EXISTS detailed_materials (
        id TEXT PRIMARY KEY,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        categoryId TEXT NOT NULL,
        unit TEXT NOT NULL,
        purchasePrice REAL NOT NULL,
        salePrice REAL NOT NULL,
        availableQuantity REAL DEFAULT 0,
        minQuantity REAL DEFAULT 0,
        supplier TEXT,
        notes TEXT,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (categoryId) REFERENCES material_categories (id)
      )`,

      // جدول مواد المشاريع
      `CREATE TABLE IF NOT EXISTS project_materials (
        id TEXT PRIMARY KEY,
        projectId TEXT NOT NULL,
        materialId TEXT NOT NULL,
        requiredQuantity REAL NOT NULL,
        usedQuantity REAL DEFAULT 0,
        totalPurchaseCost REAL NOT NULL,
        totalSaleCost REAL NOT NULL,
        profit REAL NOT NULL,
        source TEXT CHECK(source IN ('external', 'stock')) DEFAULT 'external',
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (projectId) REFERENCES projects (id),
        FOREIGN KEY (materialId) REFERENCES detailed_materials (id)
      )`,
      
      // جدول العمال
      `CREATE TABLE IF NOT EXISTS workers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        phone TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المصانع
      `CREATE TABLE IF NOT EXISTS factories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        location TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المصممين
      `CREATE TABLE IF NOT EXISTS designers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        pricePerSqm REAL NOT NULL,
        phone TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول العملاء
      `CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        totalProjects INTEGER DEFAULT 0,
        totalSpent REAL DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول المشاريع
      `CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        area REAL NOT NULL,
        furnitureType TEXT NOT NULL,
        materialId TEXT NOT NULL,
        workerId TEXT NOT NULL,
        factoryId TEXT NOT NULL,
        designerId TEXT NOT NULL,
        totalCost REAL NOT NULL,
        materialCost REAL NOT NULL,
        workerCost REAL NOT NULL,
        factoryCost REAL NOT NULL,
        designerCost REAL NOT NULL,
        paidAmount REAL DEFAULT 0,
        remainingAmount REAL NOT NULL,
        status TEXT DEFAULT 'قيد التنفيذ',
        invoiceStatus TEXT DEFAULT 'مبدئية',
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        completedAt DATETIME,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول الفواتير
      `CREATE TABLE IF NOT EXISTS invoices (
        id TEXT PRIMARY KEY,
        projectId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT,
        totalAmount REAL NOT NULL,
        status TEXT DEFAULT 'مبدئية',
        type TEXT NOT NULL,
        notes TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول الموظفين
      `CREATE TABLE IF NOT EXISTS employees (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        position TEXT NOT NULL,
        baseSalary REAL NOT NULL,
        bonuses REAL DEFAULT 0,
        deductions REAL DEFAULT 0,
        totalSalary REAL NOT NULL,
        phone TEXT,
        hireDate DATE,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // جدول معاملات الخزينة
      `CREATE TABLE IF NOT EXISTS cash_transactions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        projectId TEXT,
        employeeId TEXT,
        date DATE NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جداول التكامل الجديدة

      // جدول أحداث التكامل
      `CREATE TABLE IF NOT EXISTS integration_events (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        source TEXT NOT NULL,
        target TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        processed INTEGER DEFAULT 0,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول تكامل المواد
      `CREATE TABLE IF NOT EXISTS material_integrations (
        id TEXT PRIMARY KEY,
        material_id TEXT NOT NULL,
        project_id TEXT NOT NULL,
        required_quantity REAL NOT NULL,
        available_quantity REAL NOT NULL,
        reserved_quantity REAL DEFAULT 0,
        cost_per_unit REAL NOT NULL,
        total_cost REAL NOT NULL,
        source TEXT NOT NULL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (material_id) REFERENCES detailed_materials (id),
        FOREIGN KEY (project_id) REFERENCES projects (id)
      )`,

      // جدول تكامل العمالة
      `CREATE TABLE IF NOT EXISTS labor_integrations (
        id TEXT PRIMARY KEY,
        worker_id TEXT NOT NULL,
        project_id TEXT NOT NULL,
        estimated_hours REAL NOT NULL,
        hourly_rate REAL NOT NULL,
        total_labor_cost REAL NOT NULL,
        skill_level TEXT NOT NULL,
        availability INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        FOREIGN KEY (worker_id) REFERENCES workers (id),
        FOREIGN KEY (project_id) REFERENCES projects (id)
      )`,

      // جدول التنبيهات
      `CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        source TEXT NOT NULL,
        priority TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        read INTEGER DEFAULT 0,
        action_required INTEGER DEFAULT 0,
        related_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // جدول إعدادات التكامل
      `CREATE TABLE IF NOT EXISTS integration_config (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )`,

      // جدول تفصيل تكلفة المشاريع
      `CREATE TABLE IF NOT EXISTS project_cost_breakdowns (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        materials_cost TEXT NOT NULL,
        labor_cost TEXT NOT NULL,
        overhead_cost TEXT NOT NULL,
        total_cost REAL NOT NULL,
        profit_margin REAL NOT NULL,
        final_price REAL NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (project_id) REFERENCES projects (id)
      )`
    ];

    let completed = 0;
    const total = tables.length;

    tables.forEach((sql, index) => {
      db.run(sql, (err) => {
        if (err) {
          console.error(`خطأ في إنشاء الجدول ${index + 1}:`, err.message);
          reject(err);
          return;
        }

        completed++;
        if (completed === total) {
          console.log('تم إنشاء جميع الجداول بنجاح');
          // إضافة بيانات افتراضية للاختبار
          insertDefaultData();
          resolve();
        }
      });
    });
  });
};

// وظائف مساعدة لتنفيذ الاستعلامات
const runQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

const getQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

const allQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// إدراج البيانات الافتراضية للاختبار
const insertDefaultData = () => {
  // التحقق من وجود بيانات مسبقاً
  db.get('SELECT COUNT(*) as count FROM materials', (err, row) => {
    if (err || row.count > 0) return; // إذا كانت هناك بيانات، لا نضيف المزيد

    // إضافة مواد افتراضية
    const materials = [
      { id: '1', name: 'خشب MDF', description: 'خشب MDF عالي الجودة', pricePerSqm: 25, category: 'خشب' },
      { id: '2', name: 'خشب طبيعي', description: 'خشب طبيعي فاخر', pricePerSqm: 45, category: 'خشب' },
      { id: '3', name: 'معدن', description: 'معدن مقاوم للصدأ', pricePerSqm: 35, category: 'معدن' }
    ];

    materials.forEach(material => {
      db.run('INSERT OR IGNORE INTO materials (id, name, description, pricePerSqm, category) VALUES (?, ?, ?, ?, ?)',
        [material.id, material.name, material.description, material.pricePerSqm, material.category]);
    });

    // إضافة عمال افتراضيين
    const workers = [
      { id: '1', name: 'أحمد محمد', specialty: 'نجارة', pricePerSqm: 15, phone: '0912345678' },
      { id: '2', name: 'محمد علي', specialty: 'تجميع', pricePerSqm: 12, phone: '0923456789' }
    ];

    workers.forEach(worker => {
      db.run('INSERT OR IGNORE INTO workers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
        [worker.id, worker.name, worker.specialty, worker.pricePerSqm, worker.phone]);
    });

    // إضافة مصانع افتراضية
    const factories = [
      { id: '1', name: 'مصنع الأثاث الحديث', specialty: 'أثاث مكتبي', pricePerSqm: 20, location: 'طرابلس' },
      { id: '2', name: 'مصنع الخشب الفاخر', specialty: 'أثاث منزلي', pricePerSqm: 25, location: 'بنغازي' }
    ];

    factories.forEach(factory => {
      db.run('INSERT OR IGNORE INTO factories (id, name, specialty, pricePerSqm, location) VALUES (?, ?, ?, ?, ?)',
        [factory.id, factory.name, factory.specialty, factory.pricePerSqm, factory.location]);
    });

    // إضافة مصممين افتراضيين
    const designers = [
      { id: '1', name: 'سارة أحمد', specialty: 'تصميم داخلي', pricePerSqm: 10, phone: '0934567890' },
      { id: '2', name: 'خالد محمود', specialty: 'تصميم أثاث', pricePerSqm: 8, phone: '0945678901' }
    ];

    designers.forEach(designer => {
      db.run('INSERT OR IGNORE INTO designers (id, name, specialty, pricePerSqm, phone) VALUES (?, ?, ?, ?, ?)',
        [designer.id, designer.name, designer.specialty, designer.pricePerSqm, designer.phone]);
    });

    // إضافة فئات المواد التفصيلية
    const materialCategories = [
      { id: '1', name: 'خامات أساسية', description: 'المواد الأساسية للأثاث', color: '#3B82F6' },
      { id: '2', name: 'إكسسوارات', description: 'الإكسسوارات والمفصلات', color: '#10B981' },
      { id: '3', name: 'مواد طلاء', description: 'مواد الطلاء والتشطيب', color: '#F59E0B' },
      { id: '4', name: 'أدوات تثبيت', description: 'البراغي والمسامير', color: '#EF4444' }
    ];

    materialCategories.forEach(category => {
      db.run('INSERT OR IGNORE INTO material_categories (id, name, description, color) VALUES (?, ?, ?, ?)',
        [category.id, category.name, category.description, category.color]);
    });

    // إضافة مواد تفصيلية افتراضية
    const detailedMaterials = [
      { id: '1', code: 'MDF-18', name: 'لوح MDF 18 مم', description: 'لوح MDF بسماكة 18 مم', categoryId: '1', unit: 'لوح', purchasePrice: 25, salePrice: 35, availableQuantity: 100, minQuantity: 10 },
      { id: '2', code: 'AGT-16', name: 'لوح AGT 16 مم', description: 'لوح AGT بسماكة 16 مم', categoryId: '1', unit: 'لوح', purchasePrice: 45, salePrice: 60, availableQuantity: 50, minQuantity: 5 },
      { id: '3', code: 'H-001', name: 'مفصلة باب عادية', description: 'مفصلة باب عادية', categoryId: '2', unit: 'قطعة', purchasePrice: 2, salePrice: 4, availableQuantity: 200, minQuantity: 20 },
      { id: '4', code: 'H-002', name: 'مقبض باب', description: 'مقبض باب معدني', categoryId: '2', unit: 'قطعة', purchasePrice: 5, salePrice: 8, availableQuantity: 150, minQuantity: 15 },
      { id: '5', code: 'SEALER-1L', name: 'سيلر شفاف 1 لتر', description: 'سيلر شفاف للخشب', categoryId: '3', unit: 'لتر', purchasePrice: 15, salePrice: 25, availableQuantity: 30, minQuantity: 5 },
      { id: '6', code: 'DUCO-1L', name: 'دوكو أبيض 1 لتر', description: 'دهان دوكو أبيض', categoryId: '3', unit: 'لتر', purchasePrice: 20, salePrice: 30, availableQuantity: 25, minQuantity: 5 }
    ];

    detailedMaterials.forEach(material => {
      db.run(`INSERT OR IGNORE INTO detailed_materials (
        id, code, name, description, categoryId, unit,
        purchasePrice, salePrice, availableQuantity, minQuantity
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [material.id, material.code, material.name, material.description, material.categoryId,
         material.unit, material.purchasePrice, material.salePrice, material.availableQuantity, material.minQuantity]);
    });

    console.log('تم إدراج البيانات الافتراضية للاختبار');
  });
};

// إغلاق قاعدة البيانات
const closeDatabase = () => {
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('خطأ في إغلاق قاعدة البيانات:', err.message);
      } else {
        console.log('تم إغلاق قاعدة البيانات بنجاح');
      }
    });
  }
};

module.exports = {
  initializeDatabase,
  runQuery,
  getQuery,
  allQuery,
  closeDatabase
};
