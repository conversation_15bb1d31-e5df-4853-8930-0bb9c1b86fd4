// خدمة إدارة الحالة الموحدة
// تهدف لتوحيد إدارة الحالة عبر التطبيق وتجنب التكرار

import { ensureArray, validateFinancialData, handleError } from '@/utils/dataValidation';

// أنواع البيانات الأساسية
export interface AppState {
  projects: any[];
  materials: any[];
  employees: any[];
  customers: any[];
  cashSummary: any;
  loading: boolean;
  error: string | null;
}

// الحالة الافتراضية
const defaultState: AppState = {
  projects: [],
  materials: [],
  employees: [],
  customers: [],
  cashSummary: {
    currentBalance: 0,
    monthlyIncome: 0,
    totalIncome: 0,
    totalExpenses: 0,
    monthlyExpenses: 0,
    projectPayments: 0,
    salaryPayments: 0,
    generalExpenses: 0
  },
  loading: false,
  error: null
};

// مستمعي التغييرات
type StateListener = (state: AppState) => void;
const listeners: StateListener[] = [];

// الحالة الحالية
let currentState: AppState = { ...defaultState };

// إضافة مستمع للتغييرات
export const addStateListener = (listener: StateListener): (() => void) => {
  listeners.push(listener);
  
  // إرجاع دالة لإزالة المستمع
  return () => {
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  };
};

// إشعار جميع المستمعين بالتغييرات
const notifyListeners = () => {
  listeners.forEach(listener => {
    try {
      listener({ ...currentState });
    } catch (error) {
      handleError(error, 'إشعار مستمع الحالة');
    }
  });
};

// تحديث الحالة
export const updateState = (updates: Partial<AppState>) => {
  currentState = {
    ...currentState,
    ...updates
  };
  notifyListeners();
};

// الحصول على الحالة الحالية
export const getCurrentState = (): AppState => {
  return { ...currentState };
};

// إعادة تعيين الحالة
export const resetState = () => {
  currentState = { ...defaultState };
  notifyListeners();
};

// تحديث المشاريع
export const updateProjects = (projects: any[]) => {
  const safeProjects = ensureArray(projects);
  updateState({ projects: safeProjects });
};

// تحديث المواد
export const updateMaterials = (materials: any[]) => {
  const safeMaterials = ensureArray(materials);
  updateState({ materials: safeMaterials });
};

// تحديث الموظفين
export const updateEmployees = (employees: any[]) => {
  const safeEmployees = ensureArray(employees);
  updateState({ employees: safeEmployees });
};

// تحديث العملاء
export const updateCustomers = (customers: any[]) => {
  const safeCustomers = ensureArray(customers);
  updateState({ customers: safeCustomers });
};

// تحديث الملخص المالي
export const updateCashSummary = (cashSummary: any) => {
  const safeCashSummary = validateFinancialData(cashSummary);
  updateState({ cashSummary: safeCashSummary });
};

// تعيين حالة التحميل
export const setLoading = (loading: boolean) => {
  updateState({ loading });
};

// تعيين رسالة الخطأ
export const setError = (error: string | null) => {
  updateState({ error });
};

// حساب الإحصائيات من الحالة الحالية
export const calculateStats = () => {
  const state = getCurrentState();
  
  const activeProjects = state.projects.filter(p =>
    p.status === 'قيد التنفيذ' || p.status === 'متأخر'
  ).length;

  const completedProjects = state.projects.filter(p =>
    p.status === 'مكتمل'
  ).length;

  const pendingPayments = state.projects.reduce((sum, p) => {
    return sum + (p.remainingAmount || 0);
  }, 0);

  const totalRevenue = state.projects.reduce((sum, p) => {
    return sum + (p.paidAmount || 0);
  }, 0);

  return {
    totalProjects: state.projects.length,
    activeProjects,
    completedProjects,
    totalEmployees: state.employees.length,
    totalMaterials: state.materials.length,
    totalCustomers: state.customers.length,
    currentBalance: state.cashSummary.currentBalance,
    monthlyRevenue: state.cashSummary.monthlyIncome,
    pendingPayments,
    totalRevenue
  };
};

// Hook مخصص لاستخدام الحالة في React
export const useAppState = () => {
  const [state, setState] = React.useState(getCurrentState);

  React.useEffect(() => {
    const unsubscribe = addStateListener(setState);
    return unsubscribe;
  }, []);

  return {
    state,
    updateProjects,
    updateMaterials,
    updateEmployees,
    updateCustomers,
    updateCashSummary,
    setLoading,
    setError,
    calculateStats,
    resetState
  };
};

// تحديث البيانات من مصادر متعددة
export const refreshAllData = async () => {
  setLoading(true);
  setError(null);

  try {
    // استيراد الوظائف بشكل ديناميكي لتجنب التبعيات الدائرية
    const { getProjects, getEmployees, getMaterials, getCashSummary, getCustomers } = 
      await import('@/utils/dataManager');

    const [projects, employees, materials, cashSummary, customers] = await Promise.all([
      getProjects().catch(() => []),
      getEmployees().catch(() => []),
      getMaterials().catch(() => []),
      getCashSummary().catch(() => ({})),
      getCustomers().catch(() => [])
    ]);

    updateProjects(projects);
    updateEmployees(employees);
    updateMaterials(materials);
    updateCashSummary(cashSummary);
    updateCustomers(customers);

  } catch (error) {
    handleError(error, 'تحديث البيانات');
    setError('فشل في تحديث البيانات');
  } finally {
    setLoading(false);
  }
};

// تصدير React للاستخدام في Hook
import * as React from 'react';

// تهيئة الحالة عند بدء التطبيق
export const initializeState = async () => {
  await refreshAllData();
};

// تنظيف الموارد
export const cleanup = () => {
  listeners.length = 0;
  resetState();
};

// مساعدات للبحث والفلترة
export const searchProjects = (query: string) => {
  const state = getCurrentState();
  return state.projects.filter(project =>
    project.customerName?.toLowerCase().includes(query.toLowerCase()) ||
    project.furnitureType?.toLowerCase().includes(query.toLowerCase()) ||
    project.id?.includes(query)
  );
};

export const searchMaterials = (query: string) => {
  const state = getCurrentState();
  return state.materials.filter(material =>
    material.name?.toLowerCase().includes(query.toLowerCase()) ||
    material.category?.toLowerCase().includes(query.toLowerCase())
  );
};

export const searchEmployees = (query: string) => {
  const state = getCurrentState();
  return state.employees.filter(employee =>
    employee.name?.toLowerCase().includes(query.toLowerCase()) ||
    employee.specialty?.toLowerCase().includes(query.toLowerCase())
  );
};

// فلترة حسب الحالة
export const getProjectsByStatus = (status: string) => {
  const state = getCurrentState();
  return state.projects.filter(project => project.status === status);
};

export const getLowStockMaterials = () => {
  const state = getCurrentState();
  return state.materials.filter(material => 
    (material.availableQuantity || 0) < (material.minQuantity || 0)
  );
};

export const getActiveEmployees = () => {
  const state = getCurrentState();
  return state.employees.filter(employee => employee.status === 'نشط');
};
