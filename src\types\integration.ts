// أنواع البيانات لنظام التكامل الشامل

export interface IntegrationEvent {
  id: string;
  type: 'material_update' | 'project_created' | 'payment_received' | 'salary_calculated' | 'cost_calculated';
  source: 'cost_calculator' | 'materials' | 'treasury' | 'salaries' | 'reports';
  target: string[]; // الأقسام المستهدفة
  data: any;
  timestamp: string;
  processed: boolean;
}

export interface MaterialIntegration {
  materialId: string;
  projectId: string;
  requiredQuantity: number;
  availableQuantity: number;
  reservedQuantity: number;
  costPerUnit: number;
  totalCost: number;
  source: 'stock' | 'external';
  status: 'available' | 'insufficient' | 'reserved';
}

export interface LaborCostIntegration {
  workerId: string;
  projectId: string;
  estimatedHours: number;
  hourlyRate: number;
  totalLaborCost: number;
  skillLevel: string;
  availability: boolean;
}

export interface TreasuryIntegration {
  projectId: string;
  totalProjectCost: number;
  materialsCost: number;
  laborCost: number;
  overheadCost: number;
  expectedPayments: PaymentSchedule[];
  actualPayments: Payment[];
  profitMargin: number;
}

export interface PaymentSchedule {
  id: string;
  projectId: string;
  amount: number;
  dueDate: string;
  description: string;
  status: 'pending' | 'paid' | 'overdue';
}

export interface Payment {
  id: string;
  projectId: string;
  amount: number;
  paymentDate: string;
  paymentMethod: 'cash' | 'bank_transfer' | 'check';
  description: string;
  relatedScheduleId?: string;
}

export interface ProjectCostBreakdown {
  projectId: string;
  materialsCost: {
    basic: number;
    detailed: number;
    total: number;
  };
  laborCost: {
    workers: number;
    designers: number;
    factory: number;
    total: number;
  };
  overheadCost: {
    utilities: number;
    rent: number;
    equipment: number;
    total: number;
  };
  totalProjectCost: number;
  profitMargin: number;
  finalPrice: number;
}

export interface NotificationAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  source: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  read: boolean;
  actionRequired: boolean;
  relatedId?: string;
}

export interface IntegrationConfig {
  autoUpdateMaterials: boolean;
  autoCalculateLaborCosts: boolean;
  autoCreateInvoices: boolean;
  autoUpdateTreasury: boolean;
  enableNotifications: boolean;
  syncInterval: number; // بالدقائق
}

export interface SystemSync {
  lastSyncTime: string;
  pendingEvents: number;
  failedEvents: number;
  status: 'synced' | 'syncing' | 'error';
}
