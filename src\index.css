@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
  }

  /* تحسين الخطوط العربية */
  html[dir="rtl"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  }

  /* تحسين التمرير */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-lg;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-lg hover:bg-gray-400;
  }

  /* تحسين التركيز */
  *:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }

  /* تحسين الانتقالات */
  * {
    @apply transition-colors duration-200;
  }

  /* تحسين الأزرار */
  button {
    @apply transition-all duration-200 ease-in-out;
  }

  button:hover {
    @apply transform scale-105;
  }

  button:active {
    @apply transform scale-95;
  }

  /* تحسين البطاقات */
  .card-hover {
    @apply transition-all duration-300 ease-in-out hover:shadow-xl hover:-translate-y-1;
  }

  /* تحسين الجداول */
  table {
    @apply w-full border-collapse;
  }

  th, td {
    @apply border-b border-gray-200 px-4 py-3 text-right;
  }

  th {
    @apply bg-gray-50 font-semibold text-gray-700;
  }

  tr:hover {
    @apply bg-gray-50;
  }

  /* تحسين النماذج */
  input, textarea, select {
    @apply transition-all duration-200 ease-in-out;
  }

  input:focus, textarea:focus, select:focus {
    @apply ring-2 ring-blue-500 ring-offset-2 border-blue-500;
  }

  /* تحسين الرسوم المتحركة */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 2s infinite;
  }

  /* تحسين الاستجابة */
  @media (max-width: 768px) {
    .container {
      @apply px-4;
    }

    .grid-responsive {
      @apply grid-cols-1;
    }

    .text-responsive {
      @apply text-sm;
    }

    .button-responsive {
      @apply text-sm px-3 py-2;
    }
  }

  /* تحسين الطباعة */
  @media print {
    body {
      @apply bg-white text-black;
    }

    .no-print {
      display: none !important;
    }

    .print-break {
      page-break-after: always;
    }
  }
}