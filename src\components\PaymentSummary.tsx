import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  Clock,
  CreditCard,
  Receipt
} from 'lucide-react';
import { formatLibyanDinar } from '@/utils/calculations';
import { useAppContext } from '@/contexts/AppContext';
import { Project } from '@/utils/dataManager';

interface PaymentSummaryProps {
  projectId?: string;
  showDetails?: boolean;
}

const PaymentSummary: React.FC<PaymentSummaryProps> = ({ projectId, showDetails = true }) => {
  const { state } = useAppContext();
  const [summary, setSummary] = useState({
    totalProjects: 0,
    totalRevenue: 0,
    totalPaid: 0,
    totalRemaining: 0,
    fullyPaidProjects: 0,
    partiallyPaidProjects: 0,
    unpaidProjects: 0,
    overdueProjects: 0,
    averagePaymentPercentage: 0,
    monthlyPayments: 0,
    todayPayments: 0
  });

  useEffect(() => {
    calculateSummary();
  }, [state.projects, state.transactions, projectId]);

  const calculateSummary = () => {
    let projects = state.projects;
    
    // تصفية المشاريع إذا كان هناك مشروع محدد
    if (projectId) {
      projects = projects.filter(p => p.id === projectId);
    }

    const totalProjects = projects.length;
    const totalRevenue = projects.reduce((sum, p) => sum + p.totalCost, 0);
    const totalPaid = projects.reduce((sum, p) => sum + p.paidAmount, 0);
    const totalRemaining = projects.reduce((sum, p) => sum + p.remainingAmount, 0);

    // تصنيف المشاريع حسب حالة الدفع
    const fullyPaidProjects = projects.filter(p => p.remainingAmount === 0).length;
    const partiallyPaidProjects = projects.filter(p => p.paidAmount > 0 && p.remainingAmount > 0).length;
    const unpaidProjects = projects.filter(p => p.paidAmount === 0).length;

    // المشاريع المتأخرة (أكثر من 30 يوم ولم تكتمل الدفعة)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const overdueProjects = projects.filter(p => 
      p.remainingAmount > 0 && 
      new Date(p.createdAt) < thirtyDaysAgo
    ).length;

    // متوسط نسبة الدفع
    const averagePaymentPercentage = totalRevenue > 0 ? (totalPaid / totalRevenue) * 100 : 0;

    // الدفعات الشهرية
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthlyTransactions = state.transactions.filter(t => {
      const transactionDate = new Date(t.date);
      return t.type === 'دخل' && 
             t.category === 'مشروع' &&
             transactionDate.getMonth() === currentMonth &&
             transactionDate.getFullYear() === currentYear;
    });
    const monthlyPayments = monthlyTransactions.reduce((sum, t) => sum + t.amount, 0);

    // دفعات اليوم
    const today = new Date().toISOString().split('T')[0];
    const todayTransactions = state.transactions.filter(t => 
      t.type === 'دخل' && 
      t.category === 'مشروع' &&
      t.date === today
    );
    const todayPayments = todayTransactions.reduce((sum, t) => sum + t.amount, 0);

    setSummary({
      totalProjects,
      totalRevenue,
      totalPaid,
      totalRemaining,
      fullyPaidProjects,
      partiallyPaidProjects,
      unpaidProjects,
      overdueProjects,
      averagePaymentPercentage,
      monthlyPayments,
      todayPayments
    });
  };

  const getPaymentStatusColor = (project: Project) => {
    const percentage = (project.paidAmount / project.totalCost) * 100;
    
    if (percentage === 100) return 'text-green-600';
    if (percentage >= 50) return 'text-blue-600';
    if (percentage > 0) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPaymentStatusIcon = (project: Project) => {
    const percentage = (project.paidAmount / project.totalCost) * 100;
    
    if (percentage === 100) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (percentage >= 50) return <Clock className="h-4 w-4 text-blue-600" />;
    if (percentage > 0) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  return (
    <div className="space-y-6">
      {/* البطاقات الإحصائية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-600">{formatLibyanDinar(summary.totalRevenue)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المبالغ المحصلة</p>
                <p className="text-2xl font-bold text-blue-600">{formatLibyanDinar(summary.totalPaid)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المبالغ المتبقية</p>
                <p className="text-2xl font-bold text-orange-600">{formatLibyanDinar(summary.totalRemaining)}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">نسبة التحصيل</p>
                <p className="text-2xl font-bold text-purple-600">{summary.averagePaymentPercentage.toFixed(1)}%</p>
              </div>
              <Receipt className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* تفاصيل الدفعات */}
      {showDetails && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* إحصائيات المشاريع */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                إحصائيات المشاريع
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">مشاريع مكتملة الدفع</span>
                <div className="flex items-center gap-2">
                  <Badge className="bg-green-100 text-green-800">{summary.fullyPaidProjects}</Badge>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">مشاريع دفع جزئي</span>
                <div className="flex items-center gap-2">
                  <Badge className="bg-blue-100 text-blue-800">{summary.partiallyPaidProjects}</Badge>
                  <Clock className="h-4 w-4 text-blue-600" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">مشاريع غير مدفوعة</span>
                <div className="flex items-center gap-2">
                  <Badge className="bg-red-100 text-red-800">{summary.unpaidProjects}</Badge>
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">مشاريع متأخرة</span>
                <div className="flex items-center gap-2">
                  <Badge className="bg-orange-100 text-orange-800">{summary.overdueProjects}</Badge>
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                </div>
              </div>

              {/* شريط التقدم العام */}
              <div className="pt-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">نسبة التحصيل الإجمالية</span>
                  <span className="text-sm font-medium">{summary.averagePaymentPercentage.toFixed(1)}%</span>
                </div>
                <Progress value={summary.averagePaymentPercentage} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* الدفعات الحديثة */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                الدفعات الحديثة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">دفعات اليوم</span>
                <div className="text-right">
                  <p className="font-semibold text-green-600">{formatLibyanDinar(summary.todayPayments)}</p>
                  <p className="text-xs text-gray-500">{new Date().toLocaleDateString('ar-LY')}</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">دفعات هذا الشهر</span>
                <div className="text-right">
                  <p className="font-semibold text-blue-600">{formatLibyanDinar(summary.monthlyPayments)}</p>
                  <p className="text-xs text-gray-500">
                    {new Date().toLocaleDateString('ar-LY', { month: 'long', year: 'numeric' })}
                  </p>
                </div>
              </div>

              {/* قائمة المشاريع مع حالة الدفع */}
              {!projectId && state.projects.slice(0, 5).map((project) => (
                <div key={project.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    {getPaymentStatusIcon(project)}
                    <div>
                      <p className="text-sm font-medium">{project.customerName}</p>
                      <p className="text-xs text-gray-500">{project.furnitureType}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm font-medium ${getPaymentStatusColor(project)}`}>
                      {formatLibyanDinar(project.paidAmount)} / {formatLibyanDinar(project.totalCost)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {((project.paidAmount / project.totalCost) * 100).toFixed(0)}%
                    </p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default PaymentSummary;
