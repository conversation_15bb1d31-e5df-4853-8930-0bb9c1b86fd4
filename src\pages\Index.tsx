
import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import EnhancedCostCalculator from "@/components/enhanced/EnhancedCostCalculator";
import EnhancedNavbar from "@/components/common/EnhancedNavbar";
import PageHeader from "@/components/common/PageHeader";
import StatsGrid, { StatItem } from "@/components/common/StatsGrid";
import {
  Factory,
  Package,
  Wallet,
  FileText,
  Users,
  TrendingUp,
  Calendar,
  AlertCircle,
  BarChart3,
  Calculator,
  Receipt,
  UserCog,
  DollarSign
} from "lucide-react";
import { formatLibyanDinar } from "@/utils/calculations";
import {
  getProjects,
  getEmployees,
  getMaterials,
  getCashSummary,
  initializeDefaultData
} from "@/utils/dataManager";
import {
  ensureArray,
  validateFinancialData,
  safeReduce
} from "@/utils/dataValidation";
import { themeClasses } from "@/styles/theme";

const Index = () => {
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    totalEmployees: 0,
    totalMaterials: 0,
    currentBalance: 0,
    monthlyRevenue: 0,
    pendingPayments: 0
  });

  useEffect(() => {
    initializeDefaultData();
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const projects = await getProjects();
      const employees = await getEmployees();
      const materials = await getMaterials();
      const cashSummary = await getCashSummary();

      // استخدام خدمة التحقق الجديدة
      const safeProjects = ensureArray(projects);
      const safeEmployees = ensureArray(employees);
      const safeMaterials = ensureArray(materials);
      const safeCashSummary = validateFinancialData(cashSummary);

      const activeProjects = safeProjects.filter(p =>
        p.status === 'قيد التنفيذ' || p.status === 'متأخر'
      ).length;

      const pendingPayments = safeReduce(
        safeProjects,
        (sum, p) => sum + (p.remainingAmount || 0),
        0
      );

      setStats({
        totalProjects: safeProjects.length,
        activeProjects,
        totalEmployees: safeEmployees.length,
        totalMaterials: safeMaterials.length,
        currentBalance: safeCashSummary.currentBalance,
        monthlyRevenue: safeCashSummary.monthlyIncome,
        pendingPayments
      });
    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
      // تعيين قيم افتراضية في حالة الخطأ
      setStats({
        totalProjects: 0,
        activeProjects: 0,
        totalEmployees: 0,
        totalMaterials: 0,
        currentBalance: 0,
        monthlyRevenue: 0,
        pendingPayments: 0
      });
    }
  };

  // إعداد بيانات الإحصائيات
  const statsData: StatItem[] = [
    {
      id: 'projects',
      title: 'إجمالي المشاريع',
      value: stats.totalProjects,
      subtitle: `${stats.activeProjects} نشط`,
      icon: Factory,
      color: 'primary'
    },
    {
      id: 'materials',
      title: 'المواد المتاحة',
      value: stats.totalMaterials,
      subtitle: 'مادة مختلفة',
      icon: Package,
      color: 'secondary'
    },
    {
      id: 'employees',
      title: 'الموظفين',
      value: stats.totalEmployees,
      subtitle: 'موظف نشط',
      icon: Users,
      color: 'info'
    },
    {
      id: 'balance',
      title: 'الرصيد الحالي',
      value: formatLibyanDinar(stats.currentBalance),
      subtitle: 'دينار ليبي',
      icon: DollarSign,
      color: 'success'
    },
    {
      id: 'revenue',
      title: 'إيرادات الشهر',
      value: formatLibyanDinar(stats.monthlyRevenue),
      subtitle: 'هذا الشهر',
      icon: TrendingUp,
      color: 'warning'
    },
    {
      id: 'pending',
      title: 'مدفوعات معلقة',
      value: formatLibyanDinar(stats.pendingPayments),
      subtitle: 'في الانتظار',
      icon: AlertCircle,
      color: 'error'
    }
  ];

  const quickActions = [
    {
      title: "إدارة المواد والعمال",
      description: "إضافة وتعديل المواد والعمال والمصانع",
      icon: Package,
      path: "/materials"
    },
    {
      title: "الفواتير",
      description: "إنشاء ومتابعة الفواتير",
      icon: Receipt,
      path: "/invoices"
    },
    {
      title: "التقارير",
      description: "عرض تقارير المشاريع والأرباح",
      icon: BarChart3,
      path: "/reports"
    },
    {
      title: "الخزينة",
      description: "إدارة التدفقات النقدية",
      icon: Wallet,
      path: "/treasury"
    },
    {
      title: "الرواتب",
      description: "إدارة رواتب الموظفين",
      icon: UserCog,
      path: "/salaries"
    },
    {
      title: "العملاء",
      description: "إدارة بيانات العملاء",
      icon: Users,
      path: "/customers"
    }
  ];

  return (
    <div className={themeClasses.pageBackground}>
      <Navbar />

      {/* رأس الصفحة */}
      <PageHeader
        title="نظام إدارة مصنع الأثاث المتكامل"
        description="منصة شاملة لإدارة جميع عمليات المصنع من حساب التكاليف إلى إدارة الخزينة والموظفين"
        icon={Factory}
        gradient="primary"
      />

      <div className={themeClasses.container}>

        {/* الإحصائيات الرئيسية */}
        <StatsGrid
          stats={statsData}
          columns={3}
          className="mb-8 animate-fade-in"
        />

        {/* الإجراءات السريعة */}
        <div className={cn(themeClasses.gridCols3, 'mb-8')}>
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            const colors = [
              'bg-gradient-to-r from-blue-600 to-blue-700',
              'bg-gradient-to-r from-green-600 to-green-700',
              'bg-gradient-to-r from-purple-600 to-purple-700',
              'bg-gradient-to-r from-orange-600 to-orange-700',
              'bg-gradient-to-r from-red-600 to-red-700',
              'bg-gradient-to-r from-cyan-600 to-cyan-700'
            ];

            return (
              <Link key={action.path} to={action.path}>
                <Card className={cn(
                  'shadow-lg border-0 bg-white/80 backdrop-blur-sm cursor-pointer card-hover',
                  themeClasses.transition
                )}>
                  <CardHeader className={cn(
                    'text-white rounded-t-lg',
                    colors[index % colors.length]
                  )}>
                    <CardTitle className="flex items-center gap-3">
                      <Icon className="h-6 w-6" />
                      <span>{action.title}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <p className={themeClasses.bodyText}>{action.description}</p>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* حاسبة التكلفة */}
        <Card className={cn(
          'shadow-xl border-0 bg-white/90 backdrop-blur-sm animate-fade-in',
          themeClasses.transition
        )}>
          <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
            <CardTitle className="text-center text-2xl flex items-center justify-center gap-3">
              <Calculator className="h-8 w-8" />
              حاسبة تكلفة المشاريع
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <EnhancedCostCalculator onStatsUpdate={loadStats} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
