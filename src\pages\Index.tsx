
import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import CostCalculator from "@/components/CostCalculator";
import Navbar from "@/components/Navbar";
import StatsCard from "@/components/StatsCard";
import { 
  Factory, 
  Package, 
  Wallet, 
  FileText, 
  Users,
  TrendingUp,
  Calendar,
  AlertCircle,
  BarChart3
} from "lucide-react";
import { formatLibyanDinar } from "@/utils/calculations";
import {
  getProjects,
  getEmployees,
  getMaterials,
  getCashSummary,
  initializeDefaultData
} from "@/utils/dataManager";
import {
  ensureArray,
  validateFinancialData,
  safeReduce,
  handleError
} from "@/utils/dataValidation";

const Index = () => {
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    totalEmployees: 0,
    totalMaterials: 0,
    currentBalance: 0,
    monthlyRevenue: 0,
    pendingPayments: 0
  });

  useEffect(() => {
    initializeDefaultData();
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const projects = await getProjects();
      const employees = await getEmployees();
      const materials = await getMaterials();
      const cashSummary = await getCashSummary();

      // استخدام خدمة التحقق الجديدة
      const safeProjects = ensureArray(projects);
      const safeEmployees = ensureArray(employees);
      const safeMaterials = ensureArray(materials);
      const safeCashSummary = validateFinancialData(cashSummary);

      const activeProjects = safeProjects.filter(p =>
        p.status === 'قيد التنفيذ' || p.status === 'متأخر'
      ).length;

      const pendingPayments = safeReduce(
        safeProjects,
        (sum, p) => sum + (p.remainingAmount || 0),
        0
      );

      setStats({
        totalProjects: safeProjects.length,
        activeProjects,
        totalEmployees: safeEmployees.length,
        totalMaterials: safeMaterials.length,
        currentBalance: safeCashSummary.currentBalance,
        monthlyRevenue: safeCashSummary.monthlyIncome,
        pendingPayments
      });
    } catch (error) {
      handleError(error, 'تحميل الإحصائيات');
      // تعيين قيم افتراضية في حالة الخطأ
      setStats({
        totalProjects: 0,
        activeProjects: 0,
        totalEmployees: 0,
        totalMaterials: 0,
        currentBalance: 0,
        monthlyRevenue: 0,
        pendingPayments: 0
      });
    }
  };

  const quickActions = [
    {
      title: "إدارة المواد والمصانع",
      description: "إضافة وتعديل المواد، العمال، المصانع والمصممين",
      icon: Package,
      path: "/materials",
      color: "blue" as const
    },
    {
      title: "إدارة الخزينة",
      description: "متابعة المدفوعات والمصروفات والرصيد",
      icon: Wallet,
      path: "/treasury",
      color: "green" as const
    },
    {
      title: "التقارير والمشاريع",
      description: "عرض تقارير المشاريع والأرباح",
      icon: FileText,
      path: "/reports", 
      color: "purple" as const
    },
    {
      title: "إدارة المرتبات",
      description: "إدارة الموظفين ومرتباتهم",
      icon: Users,
      path: "/salaries",
      color: "orange" as const
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-3">
            <Factory className="h-12 w-12 text-blue-600" />
            نظام إدارة مصنع الأثاث المتكامل
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            منصة شاملة لإدارة جميع عمليات المصنع من حساب التكاليف إلى إدارة الخزينة والموظفين
          </p>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="إجمالي المشاريع"
            value={stats.totalProjects.toString()}
            description={`${stats.activeProjects} مشروع نشط`}
            icon={BarChart3}
            color="blue"
          />
          <StatsCard
            title="رصيد الخزينة"
            value={formatLibyanDinar(stats.currentBalance)}
            description="الرصيد الحالي"
            icon={Wallet}
            color="green"
          />
          <StatsCard
            title="إيرادات الشهر"
            value={formatLibyanDinar(stats.monthlyRevenue)}
            description="إيرادات الشهر الحالي"
            icon={TrendingUp}
            color="purple"
          />
          <StatsCard
            title="مدفوعات معلقة"
            value={formatLibyanDinar(stats.pendingPayments)}
            description="مبالغ متبقية من العملاء"
            icon={AlertCircle}
            color="orange"
          />
        </div>

        {/* الإجراءات السريعة */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <Link key={action.path} to={action.path}>
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all hover:scale-105 cursor-pointer">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3">
                      <div className={`p-3 rounded-lg ${
                        action.color === 'blue' ? 'bg-blue-50 text-blue-600' :
                        action.color === 'green' ? 'bg-green-50 text-green-600' :
                        action.color === 'purple' ? 'bg-purple-50 text-purple-600' :
                        'bg-orange-50 text-orange-600'
                      }`}>
                        <Icon className="h-6 w-6" />
                      </div>
                      <span>{action.title}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{action.description}</p>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* حاسبة التكلفة */}
        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-t-lg">
            <CardTitle className="text-center text-2xl">
              حاسبة تكلفة المشاريع
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <CostCalculator onStatsUpdate={loadStats} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
