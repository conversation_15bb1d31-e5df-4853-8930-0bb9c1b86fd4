// مكون رأس الصفحة الموحد
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, LucideIcon } from 'lucide-react';
import { themeClasses } from '@/styles/theme';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  showBackButton?: boolean;
  backTo?: string;
  actions?: React.ReactNode;
  className?: string;
  gradient?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error';
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  icon: Icon,
  showBackButton = false,
  backTo = '/',
  actions,
  className,
  gradient = 'primary'
}) => {
  const gradientClasses = {
    primary: 'bg-gradient-to-r from-blue-600 to-green-600',
    secondary: 'bg-gradient-to-r from-purple-600 to-blue-600',
    accent: 'bg-gradient-to-r from-orange-600 to-red-600',
    success: 'bg-gradient-to-r from-green-600 to-emerald-600',
    warning: 'bg-gradient-to-r from-yellow-600 to-orange-600',
    error: 'bg-gradient-to-r from-red-600 to-pink-600',
  };

  return (
    <header className={cn('mb-8', className)}>
      <div className={themeClasses.container}>
        {/* زر العودة */}
        {showBackButton && (
          <div className="mb-4">
            <Link to={backTo}>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                العودة
              </Button>
            </Link>
          </div>
        )}

        {/* محتوى الرأس */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-4">
            {/* الأيقونة */}
            {Icon && (
              <div className={cn(
                'p-3 rounded-xl text-white',
                gradientClasses[gradient]
              )}>
                <Icon className="h-8 w-8" />
              </div>
            )}

            {/* النصوص */}
            <div>
              <h1 className={cn(
                themeClasses.heading1,
                'flex items-center gap-3'
              )}>
                {title}
              </h1>
              {description && (
                <p className={cn(themeClasses.bodyText, 'mt-2')}>
                  {description}
                </p>
              )}
            </div>
          </div>

          {/* الإجراءات */}
          {actions && (
            <div className="flex items-center gap-2">
              {actions}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default PageHeader;
