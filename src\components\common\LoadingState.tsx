// مكونات حالة التحميل والحالات الفارغة
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertCircle, Search, Package, Users, FileText, Inbox } from 'lucide-react';
import { themeClasses } from '@/styles/theme';
import { cn } from '@/lib/utils';

// مكون التحميل
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  return (
    <RefreshCw 
      className={cn(
        'animate-spin text-blue-600',
        sizeClasses[size],
        className
      )} 
    />
  );
};

// مكون حالة التحميل الكاملة
interface LoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({ 
  message = 'جاري التحميل...', 
  size = 'lg',
  className 
}) => {
  return (
    <div className={cn(
      themeClasses.flexCenter,
      'py-12 flex-col gap-4',
      className
    )}>
      <LoadingSpinner size={size} />
      <p className={themeClasses.bodyText}>{message}</p>
    </div>
  );
};

// مكون الحالة الفارغة
interface EmptyStateProps {
  icon?: React.ComponentType<{ className?: string }>;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon = Inbox,
  title,
  description,
  action,
  className
}) => {
  return (
    <div className={cn(
      themeClasses.flexCenter,
      'py-12 flex-col gap-4 text-center',
      className
    )}>
      <div className="p-4 bg-gray-100 rounded-full">
        <Icon className="h-12 w-12 text-gray-400" />
      </div>
      <div className="space-y-2">
        <h3 className={cn(themeClasses.heading4, 'text-gray-600')}>
          {title}
        </h3>
        {description && (
          <p className={themeClasses.bodyText}>
            {description}
          </p>
        )}
      </div>
      {action && (
        <Button onClick={action.onClick} className="mt-4">
          {action.label}
        </Button>
      )}
    </div>
  );
};

// مكون حالة الخطأ
interface ErrorStateProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  className?: string;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'حدث خطأ',
  message,
  onRetry,
  className
}) => {
  return (
    <div className={cn(
      themeClasses.flexCenter,
      'py-12 flex-col gap-4 text-center',
      className
    )}>
      <div className="p-4 bg-red-100 rounded-full">
        <AlertCircle className="h-12 w-12 text-red-500" />
      </div>
      <div className="space-y-2">
        <h3 className={cn(themeClasses.heading4, 'text-red-600')}>
          {title}
        </h3>
        <p className={cn(themeClasses.bodyText, 'text-red-500')}>
          {message}
        </p>
      </div>
      {onRetry && (
        <Button 
          onClick={onRetry} 
          variant="outline"
          className="mt-4 border-red-300 text-red-600 hover:bg-red-50"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          إعادة المحاولة
        </Button>
      )}
    </div>
  );
};

// مكون حالة البحث الفارغة
interface NoSearchResultsProps {
  searchTerm: string;
  onClearSearch?: () => void;
  className?: string;
}

export const NoSearchResults: React.FC<NoSearchResultsProps> = ({
  searchTerm,
  onClearSearch,
  className
}) => {
  return (
    <div className={cn(
      themeClasses.flexCenter,
      'py-12 flex-col gap-4 text-center',
      className
    )}>
      <div className="p-4 bg-gray-100 rounded-full">
        <Search className="h-12 w-12 text-gray-400" />
      </div>
      <div className="space-y-2">
        <h3 className={cn(themeClasses.heading4, 'text-gray-600')}>
          لا توجد نتائج
        </h3>
        <p className={themeClasses.bodyText}>
          لم نجد أي نتائج لـ "{searchTerm}"
        </p>
        <p className={cn(themeClasses.smallText, 'text-gray-500')}>
          جرب البحث بكلمات مختلفة أو تحقق من الإملاء
        </p>
      </div>
      {onClearSearch && (
        <Button 
          onClick={onClearSearch} 
          variant="outline"
          className="mt-4"
        >
          مسح البحث
        </Button>
      )}
    </div>
  );
};

// مكون حالة التحميل للبطاقة
interface CardLoadingProps {
  lines?: number;
  className?: string;
}

export const CardLoading: React.FC<CardLoadingProps> = ({ 
  lines = 3, 
  className 
}) => {
  return (
    <Card className={cn('animate-pulse', className)}>
      <CardContent className="p-6">
        <div className="space-y-3">
          {Array.from({ length: lines }).map((_, index) => (
            <div
              key={index}
              className={cn(
                'h-4 bg-gray-200 rounded',
                index === 0 && 'w-3/4',
                index === 1 && 'w-1/2',
                index === 2 && 'w-2/3'
              )}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// مكون حالة التحميل للجدول
interface TableLoadingProps {
  rows?: number;
  columns?: number;
  className?: string;
}

export const TableLoading: React.FC<TableLoadingProps> = ({ 
  rows = 5, 
  columns = 4,
  className 
}) => {
  return (
    <div className={cn('animate-pulse', className)}>
      <div className="space-y-3">
        {/* رأس الجدول */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <div key={index} className="h-4 bg-gray-300 rounded" />
          ))}
        </div>
        
        {/* صفوف الجدول */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div 
            key={rowIndex} 
            className="grid gap-4" 
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div key={colIndex} className="h-4 bg-gray-200 rounded" />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

// مكون حالة التحميل للإحصائيات
export const StatsLoading: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn(themeClasses.gridCols4, 'animate-pulse', className)}>
      {Array.from({ length: 4 }).map((_, index) => (
        <Card key={index}>
          <CardContent className="p-6">
            <div className="space-y-3">
              <div className="h-6 bg-gray-200 rounded w-3/4" />
              <div className="h-8 bg-gray-300 rounded w-1/2" />
              <div className="h-4 bg-gray-200 rounded w-2/3" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

// مكون الحالات المختلفة للبيانات
interface DataStateProps {
  loading?: boolean;
  error?: string;
  empty?: boolean;
  emptyTitle?: string;
  emptyDescription?: string;
  emptyIcon?: React.ComponentType<{ className?: string }>;
  emptyAction?: {
    label: string;
    onClick: () => void;
  };
  onRetry?: () => void;
  children: React.ReactNode;
  className?: string;
}

export const DataState: React.FC<DataStateProps> = ({
  loading,
  error,
  empty,
  emptyTitle = 'لا توجد بيانات',
  emptyDescription,
  emptyIcon,
  emptyAction,
  onRetry,
  children,
  className
}) => {
  if (loading) {
    return <LoadingState className={className} />;
  }

  if (error) {
    return (
      <ErrorState 
        message={error} 
        onRetry={onRetry}
        className={className}
      />
    );
  }

  if (empty) {
    return (
      <EmptyState
        title={emptyTitle}
        description={emptyDescription}
        icon={emptyIcon}
        action={emptyAction}
        className={className}
      />
    );
  }

  return <>{children}</>;
};
