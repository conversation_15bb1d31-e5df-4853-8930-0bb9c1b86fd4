
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: string;
  description?: string;
  icon: LucideIcon;
  color?: "blue" | "green" | "orange" | "purple" | "red";
}

const StatsCard = ({ title, value, description, icon: Icon, color = "blue" }: StatsCardProps) => {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-50",
    green: "text-green-600 bg-green-50", 
    orange: "text-orange-600 bg-orange-50",
    purple: "text-purple-600 bg-purple-50",
    red: "text-red-600 bg-red-50"
  };

  return (
    <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-shadow">
      <CardHeader className="pb-3">
        <CardTitle className={`flex items-center gap-2 ${colorClasses[color]}`}>
          <div className="p-2 rounded-lg">
            <Icon className="h-6 w-6" />
          </div>
          <span className="text-gray-700">{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className={`text-3xl font-bold ${colorClasses[color].split(' ')[0]}`}>
          {value}
        </p>
        {description && (
          <p className="text-sm text-gray-600 mt-2">{description}</p>
        )}
      </CardContent>
    </Card>
  );
};

export default StatsCard;
